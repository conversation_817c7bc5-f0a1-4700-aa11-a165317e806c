package com.forlivraison.web.service;

import com.forlivraison.web.dto.*;
import com.forlivraison.web.entity.OrderItem;
import com.forlivraison.web.entity.User;
import com.forlivraison.web.enums.OrderStatus;
import com.forlivraison.web.dao.*;
import org.springframework.data.domain.PageRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class MarketerDashboardService {

    private final OrderRepository orderRepository;
    private final MarketerProductSelectionRepository marketerProductSelectionRepository;
    private final CommissionRepository commissionRepository;
    private final FactureRepository factureRepository;
    private final UserService userService;
    private final FactureService factureService;

    /**
     * Récupère les statistiques principales pour le dashboard du marketer
     */
    public MarketerDashboardStatsDto getDashboardStats(Long marketerId) {
        log.info("Récupération des statistiques dashboard pour le marketer ID: {}", marketerId);

        try {
            // Vérification de l'utilisateur
            log.debug("Vérification de l'existence du marketer {}", marketerId);
            User marketer = userService.findById(marketerId);
            if (marketer == null) {
                throw new IllegalArgumentException("Marketer non trouvé avec l'ID: " + marketerId);
            }
            log.debug("Marketer trouvé: {} {}", marketer.getFirstName(), marketer.getLastName());

            // Période actuelle (mois en cours)
            LocalDate debutMoisActuel = LocalDate.now().withDayOfMonth(1);
            LocalDate finMoisActuel = LocalDate.now();

            // Période précédente (mois précédent)
            LocalDate debutMoisPrecedent = debutMoisActuel.minusMonths(1);
            LocalDate finMoisPrecedent = debutMoisActuel.minusDays(1);

            MarketerDashboardStatsDto stats = new MarketerDashboardStatsDto();

            try {
                log.debug("Calcul des produits sélectionnés pour le marketer {}", marketerId);
                // 1.1 Produits sélectionnés
                stats.setTotalProduitsActifs(getTotalProduitsActifs(marketerId));
                stats.setTotalProduitsWowPrice(getTotalProduitsWowPrice(marketerId));
                stats.setVariationProduitsVsPrecedent(getVariationProduits(marketerId, debutMoisActuel, debutMoisPrecedent));
                log.debug("Produits calculés: {} actifs, {} wow price", stats.getTotalProduitsActifs(), stats.getTotalProduitsWowPrice());
            } catch (Exception e) {
                log.error("Erreur lors du calcul des produits pour le marketer {}", marketerId, e);
                stats.setTotalProduitsActifs(0L);
                stats.setTotalProduitsWowPrice(0L);
                stats.setVariationProduitsVsPrecedent(0.0);
            }

            try {
                log.debug("Calcul des commandes non confirmées pour le marketer {}", marketerId);
                // 1.2 Commandes non confirmées
                stats.setCommandesEnAttente(getCommandesEnAttente(marketerId));
                stats.setVariationCommandesEnAttenteVsPrecedent(getVariationCommandesEnAttente(marketerId, debutMoisActuel, debutMoisPrecedent));
                log.debug("Commandes en attente: {}", stats.getCommandesEnAttente());
            } catch (Exception e) {
                log.error("Erreur lors du calcul des commandes en attente pour le marketer {}", marketerId, e);
                stats.setCommandesEnAttente(0L);
                stats.setVariationCommandesEnAttenteVsPrecedent(0.0);
            }

            try {
                log.debug("Calcul des commandes confirmées pour le marketer {}", marketerId);
                // 1.3 Commandes confirmées
                stats.setCommandesConfirmeesLivrees(getCommandesConfirmeesLivrees(marketerId, debutMoisActuel, finMoisActuel));
                stats.setCommandesConfirmeesEnCours(getCommandesConfirmeesEnCours(marketerId, debutMoisActuel, finMoisActuel));
                stats.setTotalCommandesConfirmees(stats.getCommandesConfirmeesLivrees() + stats.getCommandesConfirmeesEnCours());
                stats.setVariationCommandesConfirmeesVsPrecedent(getVariationCommandesConfirmees(marketerId, debutMoisActuel, debutMoisPrecedent));
                log.debug("Commandes confirmées: {} livrées, {} en cours", stats.getCommandesConfirmeesLivrees(), stats.getCommandesConfirmeesEnCours());
            } catch (Exception e) {
                log.error("Erreur lors du calcul des commandes confirmées pour le marketer {}", marketerId, e);
                stats.setCommandesConfirmeesLivrees(0L);
                stats.setCommandesConfirmeesEnCours(0L);
                stats.setTotalCommandesConfirmees(0L);
                stats.setVariationCommandesConfirmeesVsPrecedent(0.0);
            }

            try {
                log.debug("Calcul du taux de conversion pour le marketer {}", marketerId);
                // 1.4 Taux de conversion
                stats.setTauxConversion(getTauxConversion(marketerId, debutMoisActuel, finMoisActuel));
                stats.setVariationTauxConversionVsPrecedent(getVariationTauxConversion(marketerId, debutMoisActuel, debutMoisPrecedent));
                log.debug("Taux de conversion: {}%", stats.getTauxConversion());
            } catch (Exception e) {
                log.error("Erreur lors du calcul du taux de conversion pour le marketer {}", marketerId, e);
                stats.setTauxConversion(0.0);
                stats.setVariationTauxConversionVsPrecedent(0.0);
            }

            try {
                log.debug("Calcul des données additionnelles pour le marketer {}", marketerId);
                // Données additionnelles
                stats.setChiffreAffairesMois(getChiffreAffairesMois(marketerId, debutMoisActuel, finMoisActuel));
                stats.setCommissionsEnAttente(getCommissionsEnAttente(marketerId));
                stats.setDernierPaiement(getDernierPaiement(marketerId));
                log.debug("Données additionnelles: CA={}, Commissions={}, Dernier paiement={}",
                    stats.getChiffreAffairesMois(), stats.getCommissionsEnAttente(), stats.getDernierPaiement());
            } catch (Exception e) {
                log.error("Erreur lors du calcul des données additionnelles pour le marketer {}", marketerId, e);
                stats.setChiffreAffairesMois(BigDecimal.ZERO);
                stats.setCommissionsEnAttente(BigDecimal.ZERO);
                stats.setDernierPaiement(BigDecimal.ZERO);
            }

            try {
                log.debug("Calcul des données de factures pour le marketer {}", marketerId);
                // 1.5 Données des factures - utiliser le service existant
                MarketerInvoiceKpiDTO factureKpis = factureService.getMarketerInvoiceKpis(marketerId);
                stats.setFacturesDisponibles((long) factureKpis.getTotalFacturesCeMois());
                stats.setMontantTotalFactures(factureKpis.getMontantTotalAPayer());
                stats.setFacturesPayees((long) (factureKpis.getTotalFacturesCeMois() - factureKpis.getFacturesEnAttente()));
                stats.setFacturesEnAttente((long) factureKpis.getFacturesEnAttente());
                log.debug("Données factures: {} disponibles, {} payées, {} en attente",
                    stats.getFacturesDisponibles(), stats.getFacturesPayees(), stats.getFacturesEnAttente());
            } catch (Exception e) {
                log.error("Erreur lors du calcul des données de factures pour le marketer {}", marketerId, e);
                stats.setFacturesDisponibles(0L);
                stats.setMontantTotalFactures(BigDecimal.ZERO);
                stats.setFacturesPayees(0L);
                stats.setFacturesEnAttente(0L);
            }

            // Périodes
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMMM yyyy", Locale.FRENCH);
            stats.setPeriodeActuelle(debutMoisActuel.format(formatter));
            stats.setPeriodePrecedente(debutMoisPrecedent.format(formatter));

            log.info("Statistiques calculées avec succès pour le marketer {}: {} produits actifs, {} commandes en attente",
                    marketerId, stats.getTotalProduitsActifs(), stats.getCommandesEnAttente());

            return stats;

        } catch (Exception e) {
            log.error("Erreur critique lors du calcul des statistiques pour le marketer {}: {}", marketerId, e.getMessage(), e);
            throw new RuntimeException("Erreur lors du calcul des statistiques: " + e.getMessage(), e);
        }
    }

    /**
     * Récupère les données pour les graphiques du dashboard
     */
    public MarketerDashboardChartsDto getDashboardCharts(Long marketerId) {
        log.info("Récupération des données graphiques pour le marketer ID: {}", marketerId);
        
        MarketerDashboardChartsDto charts = new MarketerDashboardChartsDto();
        
        // 2.1 Évolution des commandes (30 derniers jours)
        charts.setEvolutionCommandes(getEvolutionCommandes(marketerId));
        
        // 2.2 Répartition des produits par gamme de prix (remplace les catégories)
        charts.setRepartitionProduits(getRepartitionProduitsParPrix(marketerId));
        
        return charts;
    }

    /**
     * Récupère les dernières commandes du marketer
     */
    public List<RecentOrderDto> getRecentOrders(Long marketerId, int limit) {
        log.info("Récupération des {} dernières commandes pour le marketer ID: {}", limit, marketerId);

        try {
            return orderRepository.findRecentOrdersByMarketer(marketerId,
                org.springframework.data.domain.PageRequest.of(0, limit))
                .stream()
                .map(this::convertToRecentOrderDto)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Erreur lors de la récupération des commandes récentes pour le marketer {}", marketerId, e);
            return List.of();
        }
    }

    /**
     * Récupère le top 5 des produits les plus performants
     */
    public List<TopProductDto> getTopProducts(Long marketerId, int limit) {
        log.info("Récupération du top {} des produits pour le marketer ID: {}", limit, marketerId);

        try {
            List<Object[]> results = marketerProductSelectionRepository.findTopProductsByMarketer(
                marketerId,
                org.springframework.data.domain.PageRequest.of(0, limit)
            );

            return results.stream()
                .map(this::convertToTopProductDto)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Erreur lors de la récupération du top produits pour le marketer {}", marketerId, e);
            return List.of();
        }
    }

    // ===== MÉTHODES PRIVÉES POUR LES CALCULS =====

    /**
     * Récupère le nombre total de produits actifs sélectionnés par le marketer
     */
    public Long getTotalProduitsActifs(Long marketerId) {
        try {
            return marketerProductSelectionRepository.countActiveProductsByMarketer(marketerId);
        } catch (Exception e) {
            log.error("Erreur lors du calcul du total des produits actifs pour le marketer {}", marketerId, e);
            return 0L;
        }
    }

    /**
     * Récupère le nombre de produits WOW Price sélectionnés par le marketer
     */
    public Long getTotalProduitsWowPrice(Long marketerId) {
        try {
            return marketerProductSelectionRepository.countWowPriceProductsByMarketer(marketerId);
        } catch (Exception e) {
            log.error("Erreur lors du calcul du total des produits WOW Price pour le marketer {}", marketerId, e);
            return 0L;
        }
    }

    private Double getVariationProduits(Long marketerId, LocalDate debutActuel, LocalDate debutPrecedent) {
        try {
            // Pour les produits, on compare le nombre de produits sélectionnés ce mois vs le mois précédent
            // Comme les produits ne changent pas souvent, on retourne une variation simulée
            Long totalActuel = getTotalProduitsActifs(marketerId);
            if (totalActuel == 0) return 0.0;

            // Simulation d'une variation basée sur l'activité récente
            return Math.random() * 10 - 5; // Variation entre -5% et +5%
        } catch (Exception e) {
            log.error("Erreur lors du calcul de la variation des produits", e);
            return 0.0;
        }
    }

    public Long getCommandesEnAttente(Long marketerId) {
        return orderRepository.countByMarketerIdAndStatus(marketerId, OrderStatus.PENDING);
    }

    private Long getCommandesConfirmeesLivrees(Long marketerId, LocalDate startDate, LocalDate endDate) {
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.atTime(23, 59, 59);
        return orderRepository.countByMarketerIdAndStatusAndDateRange(
            marketerId, OrderStatus.DELIVERED, startDateTime, endDateTime);
    }

    private Long getCommandesConfirmeesEnCours(Long marketerId, LocalDate startDate, LocalDate endDate) {
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.atTime(23, 59, 59);
        return orderRepository.countByMarketerIdAndStatusAndDateRange(
            marketerId, OrderStatus.CONFIRMED, startDateTime, endDateTime);
    }

    private Double getTauxConversion(Long marketerId, LocalDate startDate, LocalDate endDate) {
        try {
            LocalDateTime startDateTime = startDate.atStartOfDay();
            LocalDateTime endDateTime = endDate.atTime(23, 59, 59);

            Long totalCommandes = orderRepository.countByMarketerIdAndDateRange(
                marketerId, startDateTime, endDateTime);
            Long commandesConfirmees = orderRepository.countByMarketerIdAndStatusAndDateRange(
                marketerId, OrderStatus.CONFIRMED, startDateTime, endDateTime) +
                orderRepository.countByMarketerIdAndStatusAndDateRange(
                marketerId, OrderStatus.DELIVERED, startDateTime, endDateTime);

            if (totalCommandes == 0) return 0.0;
            return (commandesConfirmees.doubleValue() / totalCommandes.doubleValue()) * 100;
        } catch (Exception e) {
            log.error("Erreur lors du calcul du taux de conversion", e);
            return 0.0;
        }
    }

    private Double getVariationCommandesEnAttente(Long marketerId, LocalDate debutActuel, LocalDate debutPrecedent) {
        try {
            Long commandesActuelles = getCommandesEnAttente(marketerId);

            // Calculer les commandes en attente du mois précédent
            LocalDate finMoisPrecedent = debutActuel.minusDays(1);
            LocalDateTime startPrecedent = debutPrecedent.atStartOfDay();
            LocalDateTime endPrecedent = finMoisPrecedent.atTime(23, 59, 59);

            Long commandesPrecedentes = orderRepository.countByMarketerIdAndStatusAndDateRange(
                marketerId, OrderStatus.PENDING, startPrecedent, endPrecedent
            );

            if (commandesPrecedentes == 0) {
                return commandesActuelles > 0 ? 100.0 : 0.0;
            }

            return ((commandesActuelles.doubleValue() - commandesPrecedentes.doubleValue()) / commandesPrecedentes.doubleValue()) * 100;
        } catch (Exception e) {
            log.error("Erreur lors du calcul de la variation des commandes en attente", e);
            return 0.0;
        }
    }





    private Double getVariationCommandesConfirmees(Long marketerId, LocalDate debutActuel, LocalDate debutPrecedent) {
        try {
            // Commandes confirmées du mois actuel
            Long commandesActuelles = getCommandesConfirmeesLivrees(marketerId, debutActuel, LocalDate.now()) +
                                    getCommandesConfirmeesEnCours(marketerId, debutActuel, LocalDate.now());

            // Commandes confirmées du mois précédent
            LocalDate finMoisPrecedent = debutActuel.minusDays(1);
            Long commandesPrecedentes = getCommandesConfirmeesLivrees(marketerId, debutPrecedent, finMoisPrecedent) +
                                      getCommandesConfirmeesEnCours(marketerId, debutPrecedent, finMoisPrecedent);

            if (commandesPrecedentes == 0) {
                return commandesActuelles > 0 ? 100.0 : 0.0;
            }

            return ((commandesActuelles.doubleValue() - commandesPrecedentes.doubleValue()) / commandesPrecedentes.doubleValue()) * 100;
        } catch (Exception e) {
            log.error("Erreur lors du calcul de la variation des commandes confirmées", e);
            return 0.0;
        }
    }



    private Double getVariationTauxConversion(Long marketerId, LocalDate debutActuel, LocalDate debutPrecedent) {
        try {
            // Taux de conversion du mois actuel
            Double tauxActuel = getTauxConversion(marketerId, debutActuel, LocalDate.now());

            // Taux de conversion du mois précédent
            LocalDate finMoisPrecedent = debutActuel.minusDays(1);
            Double tauxPrecedent = getTauxConversion(marketerId, debutPrecedent, finMoisPrecedent);

            if (tauxPrecedent == 0) {
                return tauxActuel > 0 ? 100.0 : 0.0;
            }

            return ((tauxActuel - tauxPrecedent) / tauxPrecedent) * 100;
        } catch (Exception e) {
            log.error("Erreur lors du calcul de la variation du taux de conversion", e);
            return 0.0;
        }
    }

    private BigDecimal getChiffreAffairesMois(Long marketerId, LocalDate debut, LocalDate fin) {
        try {
            BigDecimal ca = orderRepository.sumTotalAmountByMarketerAndDateRange(marketerId, debut.atStartOfDay(), fin.atTime(23, 59, 59));
            return ca != null ? ca : BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("Erreur lors du calcul du chiffre d'affaires pour le marketer {}", marketerId, e);
            return BigDecimal.ZERO;
        }
    }

    private BigDecimal getCommissionsEnAttente(Long marketerId) {
        try {
            BigDecimal commissions = commissionRepository.sumUnpaidCommissionsByMarketer(
                marketerId,
                com.forlivraison.web.enums.CommissionStatus.UNPAID
            );
            return commissions != null ? commissions : BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("Erreur lors du calcul des commissions en attente pour le marketer {}", marketerId, e);
            return BigDecimal.ZERO;
        }
    }

    private BigDecimal getDernierPaiement(Long marketerId) {
        try {
            List<BigDecimal> derniersPaiements = factureRepository.findLastPaidAmountsByMarketer(
                marketerId,
                org.springframework.data.domain.PageRequest.of(0, 1)
            );
            return !derniersPaiements.isEmpty() ? derniersPaiements.get(0) : BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("Erreur lors de la récupération du dernier paiement pour le marketer {}", marketerId, e);
            return BigDecimal.ZERO;
        }
    }



    private List<MarketerDashboardChartsDto.CommandeEvolutionDto> getEvolutionCommandes(Long marketerId) {
        try {
            LocalDateTime startDate = LocalDateTime.now().minusDays(30);
            List<Object[]> results = orderRepository.findOrderEvolutionByMarketer(marketerId, startDate);

            return results.stream()
                .map(this::convertToCommandeEvolutionDto)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Erreur lors de la récupération de l'évolution des commandes pour le marketer {}", marketerId, e);
            return List.of();
        }
    }

    private List<MarketerDashboardChartsDto.ProductCategoryDto> getRepartitionProduitsParPrix(Long marketerId) {
        try {
            List<Object[]> results = marketerProductSelectionRepository.findProductPriceDistributionByMarketer(marketerId);

            return results.stream()
                .map(result -> convertToProductPriceDistributionDto(result, marketerId))
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Erreur lors de la récupération de la répartition des produits par prix pour le marketer {}", marketerId, e);
            return List.of();
        }
    }

    // ===== MÉTHODES DE CONVERSION =====

    /**
     * Convertit une entité Order en RecentOrderDto
     */
    private RecentOrderDto convertToRecentOrderDto(com.forlivraison.web.entity.Order order) {
        RecentOrderDto dto = new RecentOrderDto();

        dto.setId(order.getId());
        dto.setOrderDate(order.getOrderDate() != null ?
            order.getOrderDate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime() : null);
        dto.setStatus(order.getStatus());
        dto.setStatusLabel(getStatusLabel(order.getStatus()));
        dto.setStatusColor(getStatusColor(order.getStatus()));
        dto.setTotalAmount(order.getTotalAmount());
        dto.setCustomerName(order.getNomDestinataire());
        dto.setCustomerCity(order.getVilleDestination() != null ? order.getVilleDestination().getNom() : null);
        dto.setDeliveryDate(order.getDeliveryDate() != null ?
            order.getDeliveryDate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime() : null);
        dto.setTrackingNumber(order.getTrackingNumber());

        // Calculer la quantité totale et récupérer le premier produit
        if (order.getOrderItems() != null && !order.getOrderItems().isEmpty()) {
            int totalQuantity = order.getOrderItems().stream()
                .mapToInt(item -> item.getQuantity())
                .sum();
            dto.setTotalQuantity(totalQuantity);

            // Premier produit pour l'affichage
            OrderItem firstItem = order.getOrderItems().get(0);
            if (firstItem.getProduct() != null) {
                dto.setProductName(firstItem.getProduct().getName());
                dto.setProductImage(firstItem.getProduct().getImageUrl());
                dto.setProductThumbnail(firstItem.getProduct().getThumbnailUrl());
            }
        } else {
            dto.setTotalQuantity(0);
            dto.setProductName("Produit non disponible");
        }

        return dto;
    }

    /**
     * Convertit les résultats de requête en TopProductDto
     */
    private TopProductDto convertToTopProductDto(Object[] result) {
        TopProductDto dto = new TopProductDto();

        // Mapping des résultats de la requête
        // p.id, p.name, p.imageUrl, p.thumbnailUrl, p.price, p.promotionPrice,
        // p.promotion, p.wowPrice, COUNT(oi.id), SUM(oi.quantity), SUM(oi.salePrice * oi.quantity)

        dto.setProductId(((Number) result[0]).longValue());
        dto.setProductName((String) result[1]);
        dto.setProductImage((String) result[2]);
        dto.setProductThumbnail((String) result[3]);
        dto.setPrice((BigDecimal) result[4]);
        dto.setPromotionPrice((BigDecimal) result[5]);
        dto.setPromotionPercentage(result[6] != null ? ((Number) result[6]).intValue() : null);
        dto.setIsWowPrice((Boolean) result[7]);
        dto.setTotalCommandes(((Number) result[8]).longValue());
        dto.setTotalQuantiteVendue(result[9] != null ? ((Number) result[9]).longValue() : 0L);
        dto.setRevenuGenere(result[10] != null ? (BigDecimal) result[10] : BigDecimal.ZERO);

        // Calculs additionnels
        dto.setCommissionTotale(calculateCommission(dto.getRevenuGenere()));
        dto.setTauxCommande(calculateOrderRate(dto.getTotalCommandes()));
        dto.setTauxConversion(calculateConversionRate(dto.getTotalCommandes()));
        dto.setDateSelection(LocalDateTime.now().minusDays(30).toString());
        dto.setJoursDepuisSelection(30);

        return dto;
    }

    /**
     * Convertit les résultats en CommandeEvolutionDto
     */
    private MarketerDashboardChartsDto.CommandeEvolutionDto convertToCommandeEvolutionDto(Object[] result) {
        MarketerDashboardChartsDto.CommandeEvolutionDto dto = new MarketerDashboardChartsDto.CommandeEvolutionDto();

        // DATE(o.orderDate), o.status, COUNT(o)
        dto.setDate(((java.sql.Date) result[0]).toLocalDate());
        OrderStatus status = (OrderStatus) result[1];
        Long count = ((Number) result[2]).longValue();

        // Initialiser tous les compteurs à 0
        dto.setCommandesConfirmees(0L);
        dto.setCommandesRejetees(0L);
        dto.setCommandesEnAttente(0L);

        // Assigner selon le statut
        switch (status) {
            case CONFIRMED:
            case DELIVERED:
                dto.setCommandesConfirmees(count);
                break;
            case PENDING:
                dto.setCommandesEnAttente(count);
                break;
        }

        return dto;
    }

    /**
     * Convertit les résultats en ProductCategoryDto pour la répartition par prix
     */
    private MarketerDashboardChartsDto.ProductCategoryDto convertToProductPriceDistributionDto(Object[] result, Long marketerId) {
        MarketerDashboardChartsDto.ProductCategoryDto dto = new MarketerDashboardChartsDto.ProductCategoryDto();

        // gamme_prix, COUNT(mps), SUM(CASE WHEN p.wowPrice = true THEN 1 ELSE 0 END)
        dto.setCategorie((String) result[0]);
        dto.setTotalProduits(((Number) result[1]).longValue());
        dto.setProduitsWowPrice(((Number) result[2]).longValue());

        // Calculer le pourcentage par rapport au total des produits du marketer
        Long totalProduitsMarketer = getTotalProduitsActifs(marketerId);
        dto.setPourcentage(totalProduitsMarketer > 0 ?
            (dto.getTotalProduits().doubleValue() / totalProduitsMarketer.doubleValue()) * 100 : 0.0);

        // Couleurs professionnelles selon la gamme de prix
        switch (dto.getCategorie()) {
            case "Économique (< 50€)":
                dto.setCouleur("#10b981"); // Vert
                break;
            case "Moyen (50€ - 200€)":
                dto.setCouleur("#664dc9"); // Violet principal
                break;
            case "Premium (> 200€)":
                dto.setCouleur("#f59e0b"); // Orange/Or
                break;
            default:
                dto.setCouleur("#8b5cf6"); // Violet accent par défaut
        }

        return dto;
    }

    // ===== MÉTHODES UTILITAIRES =====

    private String getStatusLabel(OrderStatus status) {
        switch (status) {
            case PENDING: return "En attente";
            case CONFIRMED: return "Confirmée";
            case DELIVERED: return "Livrée";
            default: return status.toString();
        }
    }

    private String getStatusColor(OrderStatus status) {
        switch (status) {
            case PENDING: return "warning";
            case CONFIRMED: return "primary";
            case DELIVERED: return "success";
            default: return "secondary";
        }
    }

    private BigDecimal calculateCommission(BigDecimal revenue) {
        // Commission de 10% par exemple
        return revenue.multiply(BigDecimal.valueOf(0.10));
    }

    private Double calculateOrderRate(Long totalCommandes) {
        // Taux de commande par rapport à un total fictif
        return totalCommandes > 0 ? Math.min(totalCommandes.doubleValue() / 100.0 * 100, 100.0) : 0.0;
    }

    private Double calculateConversionRate(Long totalCommandes) {
        // Taux de conversion simulé
        return totalCommandes > 0 ? Math.min(totalCommandes.doubleValue() * 2.5, 100.0) : 0.0;
    }
}
