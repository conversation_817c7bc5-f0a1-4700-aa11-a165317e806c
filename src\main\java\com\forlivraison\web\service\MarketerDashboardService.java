package com.forlivraison.web.service;

import com.forlivraison.web.entity.*;
import com.forlivraison.web.dao.*;
import com.forlivraison.web.dto.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service métier pour le dashboard des marketers
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class MarketerDashboardService {

    // ✅ Utilisez vos repositories existants
    private final UserRepository userRepository; // Votre repository User/Marketer
    private final ProductRepository productRepository;
    private final OrderRepository orderRepository;
    private final CommissionRepository commissionRepository;
    private final FactureRepository factureRepository;
    private final MarketerProductSelectionRepository marketerProductSelectionRepository; // ✅ Votre repository existant !

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormatter.ofPattern("MMMM yyyy", Locale.FRENCH);

    /**
     * Récupère les statistiques principales du dashboard
     */
    @Cacheable(value = "dashboard-stats", key = "#marketerId")
    public MarketerDashboardStatsDto getDashboardStats(Long marketerId) {
        log.info("📊 Calcul des statistiques pour le marketer ID: {}", marketerId);

        // Vérifier que le marketer existe
        Optional<User> marketerOpt = userRepository.findById(marketerId);
        if (!marketerOpt.isPresent()) {
            throw new RuntimeException("Marketer non trouvé avec l'ID: " + marketerId);
        }

        // Périodes de calcul
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfCurrentMonth = now.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        LocalDateTime startOfPreviousMonth = startOfCurrentMonth.minusMonths(1);
        LocalDateTime endOfPreviousMonth = startOfCurrentMonth.minusSeconds(1);

        // 1. Statistiques des produits (utilise VOTRE repository !)
        Long totalProduitsActifs = marketerProductSelectionRepository.countActiveProductsByMarketer(marketerId);
        Long totalProduitsWowPrice = marketerProductSelectionRepository.countWowPriceProductsByMarketer(marketerId);

        // 2. Statistiques des commandes (adaptez selon vos entités)
        OrderStats currentOrderStats = calculateOrderStats(marketerId, startOfCurrentMonth, now);
        OrderStats previousOrderStats = calculateOrderStats(marketerId, startOfPreviousMonth, endOfPreviousMonth);

        // 3. Statistiques des commissions (adaptez selon vos entités)
        CommissionStats commissionStats = calculateCommissionStats(marketerId, startOfCurrentMonth, now);

        // 4. Statistiques des factures (adaptez selon vos entités)
        InvoiceStats invoiceStats = calculateInvoiceStats(marketerId, startOfCurrentMonth, now);

        // 5. Calcul des variations
        double productVariation = 0.0; // Calculez si vous avez l'historique
        double orderConfirmedVariation = calculateVariation(currentOrderStats.confirmees, previousOrderStats.confirmees);
        double conversionVariation = calculateVariation(currentOrderStats.tauxConversion, previousOrderStats.tauxConversion);

        MarketerDashboardStatsDto stats = MarketerDashboardStatsDto.builder()
                // Produits (utilise VOTRE repository !)
                .totalProduitsActifs(totalProduitsActifs.intValue())
                .totalProduitsWowPrice(totalProduitsWowPrice.intValue())
                .variationProduitsVsPrecedent(productVariation)

                // Commandes
                .commandesEnAttente(currentOrderStats.enAttente)
                .totalCommandesConfirmees(currentOrderStats.confirmees)
                .commandesConfirmeesLivrees(currentOrderStats.livrees)
                .commandesConfirmeesEnCours(currentOrderStats.enCours)
                .variationCommandesConfirmeesVsPrecedent(orderConfirmedVariation)

                // Taux de conversion
                .tauxConversion(currentOrderStats.tauxConversion)
                .variationTauxConversionVsPrecedent(conversionVariation)

                // Données financières
                .chiffreAffairesMois(currentOrderStats.chiffreAffaires)
                .commissionsEnAttente(commissionStats.enAttente)
                .dernierPaiement(commissionStats.dernierPaiement)

                // Factures
                .facturesDisponibles(invoiceStats.disponibles)
                .montantTotalFactures(invoiceStats.montantTotal)
                .facturesPayees(invoiceStats.payees)
                .facturesEnAttenteFacture(invoiceStats.enAttente)

                // Périodes
                .periodeActuelle(now.format(MONTH_FORMATTER))
                .periodePrecedente(startOfPreviousMonth.format(MONTH_FORMATTER))
                .build();

        log.info("✅ Statistiques calculées pour le marketer ID: {} - Produits: {}, WOW: {}, Commandes: {}",
                marketerId, stats.getTotalProduitsActifs(), stats.getTotalProduitsWowPrice(), stats.getTotalCommandesConfirmees());

        return stats;
    }

    /**
     * Récupère les données pour les graphiques
     */
    @Cacheable(value = "dashboard-charts", key = "#marketerId")
    public MarketerDashboardChartsDto getDashboardCharts(Long marketerId) {
        log.info("📈 Calcul des données graphiques pour le marketer ID: {}", marketerId);

        // 1. Évolution des commandes (30 derniers jours)
        List<CommandeEvolutionDto> evolution = calculateOrderEvolution(marketerId, 30);

        // 2. Répartition des produits par gamme de prix (utilise VOTRE méthode !)
        List<ProductCategoryDto> repartition = calculateProductDistributionFromYourRepo(marketerId);

        MarketerDashboardChartsDto charts = MarketerDashboardChartsDto.builder()
                .evolutionCommandes(evolution)
                .repartitionProduits(repartition)
                .build();

        log.info("✅ Données graphiques calculées pour le marketer ID: {} - {} points d'évolution, {} catégories",
                marketerId, evolution.size(), repartition.size());

        return charts;
    }

    /**
     * Récupère les commandes récentes
     */
    public List<RecentOrderDto> getRecentOrders(Long marketerId, int limit) {
        log.info("📦 Récupération des {} dernières commandes pour le marketer ID: {}", limit, marketerId);

        // TODO: Adaptez selon votre entité Order et la relation avec User/Marketer
        // List<Order> orders = orderRepository.findTopNByMarketerIdOrderByOrderDateDesc(marketerId, limit);

        // Pour l'instant, données d'exemple (remplacez par votre logique)
        List<RecentOrderDto> recentOrders = generateSampleOrders(limit);

        log.info("✅ {} commandes récentes récupérées pour le marketer ID: {}", recentOrders.size(), marketerId);
        return recentOrders;
    }

    /**
     * Récupère le top des produits performants (utilise VOTRE repository !)
     */
    public List<TopProductDto> getTopProducts(Long marketerId, int limit) {
        log.info("🏆 Récupération du top {} produits pour le marketer ID: {}", limit, marketerId);

        // ✅ Utilise VOTRE méthode existante !
        List<Object[]> topProductStats = marketerProductSelectionRepository
                .findTopProductsByMarketer(marketerId, PageRequest.of(0, limit));

        List<TopProductDto> topProducts = topProductStats.stream()
                .map(this::mapYourStatsToTopProductDto)
                .collect(Collectors.toList());

        log.info("✅ Top {} produits récupérés pour le marketer ID: {}", topProducts.size(), marketerId);
        return topProducts;
    }

    /**
     * Récupère les informations du marketer
     */
    public MarketerInfoDto getMarketerInfo(Long marketerId) {
        log.info("👤 Récupération des informations pour le marketer ID: {}", marketerId);

        Optional<User> marketerOpt = userRepository.findById(marketerId);
        if (!marketerOpt.isPresent()) {
            throw new RuntimeException("Marketer non trouvé avec l'ID: " + marketerId);
        }

        User marketer = marketerOpt.get();

        MarketerInfoDto info = MarketerInfoDto.builder()
                .id(marketer.getId())
                .firstName(marketer.getFirstName()) // Adaptez selon vos champs
                .lastName(marketer.getLastName())   // Adaptez selon vos champs
                .email(marketer.getEmail())         // Adaptez selon vos champs
                .telephone(marketer.getTelephone()) // Adaptez selon vos champs
                .subscriptionType("PREMIUM")        // Adaptez selon votre modèle
                .status("ACTIVE")                   // Adaptez selon votre modèle
                .build();

        log.info("✅ Informations récupérées pour le marketer: {} {}", info.getFirstName(), info.getLastName());
        return info;
    }

    // ==================== MÉTHODES PRIVÉES ADAPTÉES ====================

    /**
     * Calcule la répartition des produits en utilisant VOTRE repository existant
     */
    private List<ProductCategoryDto> calculateProductDistributionFromYourRepo(Long marketerId) {
        // ✅ Utilise VOTRE méthode de répartition par gamme de prix !
        List<Object[]> priceDistribution = marketerProductSelectionRepository
                .findProductPriceDistributionByMarketer(marketerId);

        List<ProductCategoryDto> distribution = new ArrayList<>();

        // Couleurs pour les gammes de prix
        String[] colors = {"#10B981", "#F59E0B", "#664DC9"}; // Vert, Orange, Violet
        int colorIndex = 0;

        int totalProducts = priceDistribution.stream()
                .mapToInt(row -> ((Number) row[1]).intValue())
                .sum();

        for (Object[] row : priceDistribution) {
            String gamme = (String) row[0];           // 'Économique (< 50€)', etc.
            int totalProduits = ((Number) row[1]).intValue();
            int produitsWowPrice = ((Number) row[2]).intValue();

            double pourcentage = totalProducts > 0 ? (totalProduits * 100.0) / totalProducts : 0.0;
            pourcentage = Math.round(pourcentage * 10.0) / 10.0;

            ProductCategoryDto categoryDto = ProductCategoryDto.builder()
                    .categorie(gamme)
                    .totalProduits(totalProduits)
                    .produitsWowPrice(produitsWowPrice)
                    .pourcentage(pourcentage)
                    .couleur(colors[colorIndex % colors.length])
                    .build();

            distribution.add(categoryDto);
            colorIndex++;
        }

        return distribution;
    }

    /**
     * Mappe VOS statistiques vers TopProductDto
     */
    private TopProductDto mapYourStatsToTopProductDto(Object[] stats) {
        // Selon votre requête :
        // p.id, p.name, p.imageUrl, p.thumbnailUrl, p.price, p.promotionPrice,
        // p.promotion, isWowPrice, totalCommandes, totalQuantite, revenu

        Long productId = (Long) stats[0];
        String productName = (String) stats[1];
        String imageUrl = (String) stats[2];
        String thumbnailUrl = (String) stats[3];
        BigDecimal price = (BigDecimal) stats[4];
        BigDecimal promotionPrice = (BigDecimal) stats[5];
        Double promotion = (Double) stats[6];
        Boolean isWowPrice = (Boolean) stats[7];
        Long totalCommandes = (Long) stats[8];
        Long totalQuantite = (Long) stats[9];
        BigDecimal revenu = (BigDecimal) stats[10];

        // Calculer la commission (exemple: 10% du revenu)
        double commissionTotale = revenu != null ? revenu.multiply(BigDecimal.valueOf(0.10)).doubleValue() : 0.0;

        return TopProductDto.builder()
                .productId(productId)
                .productName(productName)
                .productImage(imageUrl)
                .productThumbnail(thumbnailUrl)
                .price(price != null ? price.doubleValue() : 0.0)
                .promotionPrice(promotionPrice != null ? promotionPrice.doubleValue() : null)
                .promotionPercentage(promotion)
                .isWowPrice(isWowPrice != null && isWowPrice)
                .totalCommandes(totalCommandes != null ? totalCommandes.intValue() : 0)
                .totalQuantiteVendue(totalQuantite != null ? totalQuantite.intValue() : 0)
                .revenuGenere(revenu != null ? revenu.doubleValue() : 0.0)
                .commissionTotale(commissionTotale)
                .tauxConversion(85.0) // TODO: Calculez le vrai taux
                .build();
    }

    /**
     * Calcule l'évolution des commandes sur N jours
     */
    private List<CommandeEvolutionDto> calculateOrderEvolution(Long marketerId, int days) {
        List<CommandeEvolutionDto> evolution = new ArrayList<>();
        LocalDate endDate = LocalDate.now();

        for (int i = days - 1; i >= 0; i--) {
            LocalDate date = endDate.minusDays(i);

            // TODO: Adaptez selon votre entité Order
            // Pour l'instant, données d'exemple
            CommandeEvolutionDto dayEvolution = CommandeEvolutionDto.builder()
                    .date(date.format(DATE_FORMATTER))
                    .commandesConfirmees((int)(Math.random() * 8) + 1)
                    .commandesRejetees((int)(Math.random() * 3))
                    .commandesEnAttente((int)(Math.random() * 5))
                    .build();

            evolution.add(dayEvolution);
        }
        return evolution;
    }

    /**
     * Calcule les statistiques des commandes (vraie implémentation)
     */
    private OrderStats calculateOrderStats(Long marketerId, LocalDateTime start, LocalDateTime end) {
        try {
            // Commandes en attente (sans filtre de date)
            Long enAttente = orderRepository.countByMarketerIdAndStatus(marketerId, com.forlivraison.web.enums.OrderStatus.PENDING);

            // Commandes confirmées dans la période (utilise LocalDateTime)
            Long confirmees = orderRepository.countByMarketerIdAndStatusAndLocalDateTimeRange(
                marketerId, com.forlivraison.web.enums.OrderStatus.CONFIRMED, start, end);

            // Commandes livrées dans la période (utilise LocalDateTime)
            Long livrees = orderRepository.countByMarketerIdAndStatusAndLocalDateTimeRange(
                marketerId, com.forlivraison.web.enums.OrderStatus.DELIVERED, start, end);

            // Total des commandes dans la période (utilise LocalDateTime)
            Long totalCommandes = orderRepository.countByMarketerIdAndDateRange(marketerId, start, end);

            // Calcul du taux de conversion
            double tauxConversion = 0.0;
            if (totalCommandes > 0) {
                tauxConversion = ((confirmees.doubleValue() + livrees.doubleValue()) / totalCommandes.doubleValue()) * 100.0;
            }

            // Chiffre d'affaires du mois (utilise LocalDateTime)
            BigDecimal ca = orderRepository.sumTotalAmountByMarketerAndDateRange(marketerId, start, end);
            double chiffreAffaires = ca != null ? ca.doubleValue() : 0.0;

            return new OrderStats(
                enAttente.intValue(),
                (confirmees.intValue() + livrees.intValue()),
                livrees.intValue(),
                confirmees.intValue(),
                tauxConversion,
                chiffreAffaires
            );

        } catch (Exception e) {
            log.error("Erreur lors du calcul des statistiques de commandes pour le marketer {}", marketerId, e);
            return new OrderStats(0, 0, 0, 0, 0.0, 0.0);
        }
    }

    /**
     * Calcule les statistiques des commissions (à adapter selon vos entités)
     */
    private CommissionStats calculateCommissionStats(Long marketerId, LocalDateTime start, LocalDateTime end) {
        // TODO: Adaptez selon votre entité Commission
        return new CommissionStats(1250.75, 850.25);
    }

    /**
     * Calcule les statistiques des factures (à adapter selon vos entités)
     */
    private InvoiceStats calculateInvoiceStats(Long marketerId, LocalDateTime start, LocalDateTime end) {
        // TODO: Adaptez selon votre entité Facture
        return new InvoiceStats(8, 5, 3, 5420.80);
    }

    /**
     * Calcule la variation en pourcentage
     */
    private double calculateVariation(double current, double previous) {
        if (previous == 0) {
            return current > 0 ? 100.0 : 0.0;
        }
        double variation = ((current - previous) / previous) * 100.0;
        return Math.round(variation * 10.0) / 10.0;
    }

    /**
     * Génère des données d'exemple pour les commandes (à remplacer)
     */
    private List<RecentOrderDto> generateSampleOrders(int limit) {
        List<RecentOrderDto> orders = new ArrayList<>();
        for (int i = 0; i < limit; i++) {
            orders.add(RecentOrderDto.builder()
                    .id((long)(1000 + i))
                    .orderDate(LocalDate.now().minusDays(i).format(DATE_FORMATTER))
                    .status("CONFIRMED")
                    .statusLabel("Confirmée")
                    .statusColor("success")
                    .productName("Produit " + (i + 1))
                    .productImage("/uploads/product" + (i + 1) + ".jpg")
                    .productThumbnail("/uploads/thumb" + (i + 1) + ".jpg")
                    .totalQuantity(1 + (int)(Math.random() * 3))
                    .totalAmount(99.99 + (Math.random() * 500))
                    .customerName("Client " + (i + 1))
                    .customerCity("Ville " + (i + 1))
                    .build());
        }
        return orders;
    }

    // ==================== CLASSES INTERNES ====================

    private static class OrderStats {
        final int enAttente;
        final int confirmees;
        final int livrees;
        final int enCours;
        final double tauxConversion;
        final double chiffreAffaires;

        OrderStats(int enAttente, int confirmees, int livrees, int enCours, double tauxConversion, double chiffreAffaires) {
            this.enAttente = enAttente;
            this.confirmees = confirmees;
            this.livrees = livrees;
            this.enCours = enCours;
            this.tauxConversion = tauxConversion;
            this.chiffreAffaires = chiffreAffaires;
        }
    }

    private static class CommissionStats {
        final double enAttente;
        final double dernierPaiement;

        CommissionStats(double enAttente, double dernierPaiement) {
            this.enAttente = enAttente;
            this.dernierPaiement = dernierPaiement;
        }
    }

    private static class InvoiceStats {
        final int disponibles;
        final int payees;
        final int enAttente;
        final double montantTotal;

        InvoiceStats(int disponibles, int payees, int enAttente, double montantTotal) {
            this.disponibles = disponibles;
            this.payees = payees;
            this.enAttente = enAttente;
            this.montantTotal = montantTotal;
        }
    }
}
