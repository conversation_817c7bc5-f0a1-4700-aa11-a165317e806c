package com.forlivraison.web.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * DTO pour la répartition des produits par catégorie
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Répartition des produits par catégorie")
public class ProductCategoryDto {

    @Schema(description = "Nom de la catégorie")
    private String categorie;

    @Schema(description = "Nombre total de produits dans cette catégorie")
    private Integer totalProduits;

    @Schema(description = "Nombre de produits WOW Price dans cette catégorie")
    private Integer produitsWowPrice;

    @Schema(description = "Pourcentage de cette catégorie")
    private Double pourcentage;

    @Schema(description = "Couleur pour le graphique")
    private String couleur;
}
