// Variables de couleurs professionnelles Affilink
$primary-color: #664DC9;        // Primaire (violet foncé)
$secondary-color: #A8A4F2;      // Secondaire lavande
$text-dark: #2F2F2F;            // Texte foncé
$text-light: #828282;           // Texte clair
$bg-alt: #F4F3FC;               // Fond alternatif
$border-color: #DCDCE6;         // Bordures
$success-color: #10B981;        // Succès
$error-color: #EF4444;          // Erreur

// Anciennes variables pour compatibilité
$accent-color: $secondary-color;
$warning-color: #f59e0b;
$danger-color: $error-color;
$light-bg: $bg-alt;
$card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

// Optimisations globales pour réduire l'espace
:host {
  display: block;
  padding: 0.75rem; // Réduction du padding global

  .mb-5 {
    margin-bottom: 1.5rem !important; // Réduction des marges (3rem -> 1.5rem)
  }

  .mb-4 {
    margin-bottom: 1rem !important; // Réduction des marges (1.5rem -> 1rem)
  }
}

// Dashboard Header moderne
.dashboard-header {
  background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
  border-radius: 16px;
  padding: 0.75rem 2rem; // Réduction encore plus importante du padding vertical
  margin-bottom: 1rem; // Réduction de l'espacement inférieur
  color: white;
  box-shadow: 0 8px 32px rgba($primary-color, 0.3);

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1.5rem;
  }

  .welcome-section {
    flex: 1;
    min-width: 300px;

    .welcome-title {
      font-size: 1.25rem; // Réduction de la taille (1.5rem -> 1.25rem)
      font-weight: 700;
      margin: 0 0 0.25rem 0; // Réduction de l'espacement
      color: white;
      line-height: 1.2;
    }

    .welcome-subtitle {
      font-size: 0.875rem; // Réduction de la taille (1rem -> 0.875rem)
      margin: 0;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 400;
    }
  }

  .header-controls {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex-wrap: wrap;
  }

  .period-selector {
    display: flex;
    align-items: center;
    gap: 0.75rem;

    .period-label {
      font-weight: 400;
      color: rgba(255, 255, 255, 0.9);
      margin: 0;
      font-size: 0.875rem;
    }

    .period-display {
      .period-badge {
        background: rgba(255, 255, 255, 0.25);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 600;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
      }
    }
  }

  .refresh-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    font-weight: 500;

    &:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-1px);
    }

    &:disabled {
      background: rgba(255, 255, 255, 0.1);
      color: rgba(255, 255, 255, 0.6);
    }

    mat-icon {
      margin-right: 0.5rem;
    }
  }
}

// Section Titles
.section-title {
  color: #374151;
  font-weight: 600;
  font-size: 1.25rem; // Réduction de la taille (1.5rem -> 1.25rem)
  margin-bottom: 1rem; // Réduction de l'espacement
  display: flex;
  align-items: center;

  mat-icon {
    color: $primary-color;
    font-size: 1.25rem; // Réduction de l'icône aussi
    margin-right: 0.5rem;
  }
}

// KPI Section moderne
.kpi-section {
  .kpi-grid {
    display: flex;
    flex-wrap: nowrap;
    gap: 1rem;
    margin-top: 1.5rem;
    overflow-x: auto;
    padding-bottom: 0.5rem;

    // Scroll horizontal discret
    &::-webkit-scrollbar {
      height: 5px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: $primary-color;
      border-radius: 2px;
    }
  }

  .kpi-card-wrapper {
    position: relative;
    min-width: 180px; // Réduction de la largeur minimale (200px -> 180px)
    flex: 1; // Répartition équitable de l'espace
    max-width: 220px; // Largeur maximale pour éviter que les cartes soient trop larges
  }

  .modern-card {
    background: white;
    border-radius: 16px;
    padding: 0.75rem; // Réduction encore plus importante du padding (1rem -> 0.75rem)
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid $border-color;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    height: 130px; // Hauteur encore plus compacte (150px -> 130px)

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, $primary-color, $secondary-color);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover::before {
      opacity: 1;
    }
  }

  .kpi-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem; // Réduction de l'espacement

    .kpi-icon-wrapper {
      width: 32px; // Réduction encore plus importante (36px -> 32px)
      height: 32px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 0.5rem; // Réduction de l'espacement
      transition: transform 0.3s ease;

      .kpi-icon {
        font-size: 16px; // Réduction encore plus importante (18px -> 16px)
        width: 16px;
        height: 16px;
      }
    }

    .kpi-title {
      font-size: 0.7rem; // Réduction encore plus importante (0.75rem -> 0.7rem)
      font-weight: 600;
      color: $text-dark;
      margin: 0;
      line-height: 1.3; // Réduction de l'interligne
    }
  }

  .kpi-value-section {
    margin-bottom: 0.4rem; // Réduction encore plus importante de l'espacement

    .kpi-value {
      font-size: 1.3rem; // Réduction encore plus importante (1.5rem -> 1.3rem)
      font-weight: 700;
      margin: 0;
      line-height: 1.1; // Réduction de l'interligne
      color: $text-dark;
    }
  }

  .kpi-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.65rem; // Réduction de la taille (0.75rem -> 0.65rem)

    .kpi-variation {
      display: flex;
      align-items: center;
      font-weight: 600;
      padding: 0.25rem 0.5rem;
      border-radius: 6px;

      .variation-icon {
        font-size: 14px;
        width: 14px;
        height: 14px;
        margin-right: 0.25rem;
      }

      .variation-value {
        font-weight: 600;
      }

      &.text-success {
        background: rgba($success-color, 0.1);
        color: $success-color;
      }

      &.text-danger {
        background: rgba($error-color, 0.1);
        color: $error-color;
      }
    }

    .variation-label,
    .no-comparison {
      color: $text-light;
      font-weight: 500;
    }
  }

  .kpi-progress {
    margin-top: 0.75rem;
    height: 4px;
    background: $bg-alt;
    border-radius: 2px;
    overflow: hidden;

    .progress-bar {
      height: 100%;
      background: linear-gradient(90deg, $primary-color, $secondary-color);
      border-radius: 2px;
      transition: width 0.8s ease;
    }
  }

  // Couleurs spécifiques par type de carte
  .kpi-primary {
    .kpi-icon-wrapper {
      background: rgba($primary-color, 0.1);
      .kpi-icon { color: $primary-color; }
    }
    .kpi-value { color: $primary-color; }
  }

  .kpi-success {
    .kpi-icon-wrapper {
      background: rgba($success-color, 0.1);
      .kpi-icon { color: $success-color; }
    }
    .kpi-value { color: $success-color; }
  }

  .kpi-warning {
    .kpi-icon-wrapper {
      background: rgba(#f59e0b, 0.1);
      .kpi-icon { color: #f59e0b; }
    }
    .kpi-value { color: #f59e0b; }
  }

  .kpi-accent {
    .kpi-icon-wrapper {
      background: rgba($secondary-color, 0.1);
      .kpi-icon { color: $secondary-color; }
    }
    .kpi-value { color: $secondary-color; }
  }

  .kpi-info {
    .kpi-icon-wrapper {
      background: rgba(#3b82f6, 0.1);
      .kpi-icon { color: #3b82f6; }
    }
    .kpi-value { color: #3b82f6; }
  }

  .kpi-secondary {
    .kpi-icon-wrapper {
      background: rgba($text-light, 0.1);
      .kpi-icon { color: $text-light; }
    }
    .kpi-value { color: $text-light; }
  }
}

// Skeleton Loader moderne
.skeleton-card {
  .skeleton-loader {
    .skeleton-header {
      display: flex;
      align-items: center;
      margin-bottom: 1rem;

      .skeleton-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        margin-right: 0.75rem;
      }

      .skeleton-title {
        flex: 1;
        height: 1rem;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 4px;
      }
    }

    .skeleton-value {
      height: 2rem;
      width: 70%;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
      border-radius: 4px;
      margin-bottom: 1rem;
    }

    .skeleton-footer {
      .skeleton-variation {
        height: 0.75rem;
        width: 50%;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 4px;
      }
    }
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// Charts Section moderne avec ApexCharts
.charts-section {
  .charts-grid {
    display: grid;
    grid-template-columns: 2fr 1fr; // Évolution à gauche (plus large), colonne droite plus étroite
    gap: 0.75rem; // Réduction encore plus importante de l'espacement
    margin-top: 0.75rem; // Réduction de l'espacement supérieur
    align-items: start; // Alignement en haut

    .chart-card-wrapper {
      position: relative;

      &.full-width {
        grid-column: 1 / -1;
      }
    }

    // Layout spécifique pour le nouveau design
    .evolution-chart {
      grid-row: 1 / 3; // Prend toute la hauteur à gauche

      .modern-chart {
        height: 100%; // Prend toute la hauteur disponible
        min-height: 320px; // Hauteur minimale pour le graphique d'évolution
      }
    }

    .right-charts-column {
      display: flex;
      flex-direction: column;
      gap: 0.75rem; // Réduction de l'espacement entre les graphiques
      height: 320px; // Même hauteur que le graphique d'évolution

      .donut-chart {
        flex: 1; // Prend la moitié de l'espace disponible
        min-height: 150px; // Hauteur minimale

        .modern-chart {
          height: 100%;
          display: flex;
          flex-direction: column;
        }

        .chart-content {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .bar-chart {
        flex: 1; // Prend la moitié de l'espace disponible
        min-height: 150px; // Hauteur minimale

        .modern-chart {
          height: 100%;
          display: flex;
          flex-direction: column;
        }

        .chart-content {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .modern-chart {
      background: white;
      border-radius: 16px;
      padding: 1rem; // Réduction du padding (1.5rem -> 1rem)
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid $border-color;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      height: 100%; // Prend toute la hauteur disponible

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, $primary-color, $secondary-color);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover::before {
        opacity: 1;
      }
    }

    .chart-header {
      margin-bottom: 0.75rem; // Réduction encore plus importante de l'espacement

      .chart-title {
        font-size: 0.9rem; // Réduction encore plus importante (1rem -> 0.9rem)
        font-weight: 700;
        color: $text-dark;
        margin: 0 0 0.2rem 0; // Réduction de l'espacement
        line-height: 1.3;
      }

      .chart-subtitle {
        font-size: 0.7rem; // Réduction encore plus importante (0.75rem -> 0.7rem)
        color: $text-light;
        margin: 0;
        font-weight: 500;
      }
    }

    .chart-content {
      position: relative;
      flex: 1; // Prend tout l'espace disponible
      min-height: 180px; // Réduction encore plus importante (220px -> 180px)
      display: flex;
      flex-direction: column;

      // Styles pour ApexCharts
      ::ng-deep {
        .apexcharts-canvas {
          margin: 0 auto;
        }

        .apexcharts-legend {
          justify-content: center !important;
        }

        .apexcharts-tooltip {
          background: rgba(255, 255, 255, 0.95) !important;
          border: 1px solid $border-color !important;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
          border-radius: 8px !important;
        }

        .apexcharts-tooltip-title {
          background: $light-bg !important;
          border-bottom: 1px solid $border-color !important;
          font-weight: 600 !important;
          color: $text-dark !important;
        }

        .apexcharts-tooltip-series-group {
          background: transparent !important;
        }

        .apexcharts-tooltip-text-y-label,
        .apexcharts-tooltip-text-y-value {
          color: $text-dark !important;
          font-weight: 500 !important;
        }
      }
    }

    // Skeleton loader pour les graphiques
    .skeleton-chart {
      .chart-skeleton {
        .skeleton-header {
          margin-bottom: 1.5rem;

          .skeleton-title {
            height: 1.125rem;
            width: 60%;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 4px;
            margin-bottom: 0.5rem;
          }

          .skeleton-subtitle {
            height: 0.875rem;
            width: 40%;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 4px;
          }
        }

        .skeleton-chart-area {
          height: 300px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: $text-light;

          p {
            margin-top: 1rem;
            font-size: 0.875rem;
            font-weight: 500;
          }
        }
      }
    }
  }

  // Responsive pour les graphiques
  @media (max-width: 768px) {
    .charts-grid {
      grid-template-columns: 1fr;
      gap: 1rem;

      .chart-card-wrapper.full-width {
        grid-column: 1;
      }

      .modern-chart {
        padding: 1rem;

        .chart-content {
          min-height: 300px;
        }
      }
    }
  }
}

// Tables Section
.tables-section {
  margin-top: 0.5rem; // Réduction de l'espacement supérieur

  .table-card {
    border-radius: 12px;
    box-shadow: $card-shadow;

    mat-card-header {
      background-color: $light-bg;
      border-radius: 12px 12px 0 0;
      padding: 0.75rem 1rem; // Réduction du padding

      mat-card-title {
        color: #374151;
        font-weight: 600;
        font-size: 0.9rem; // Réduction de la taille
        display: flex;
        align-items: center;
      }

      mat-card-subtitle {
        color: #6b7280;
        font-size: 0.75rem; // Réduction de la taille
      }
    }

    mat-card-content {
      padding: 0.75rem; // Réduction du padding
    }

    .table-responsive {
      max-height: 250px; // Réduction encore plus importante (300px -> 250px)
      overflow-y: auto;
    }

    .table {
      margin-bottom: 0;

      th {
        background-color: $light-bg;
        border-top: none;
        font-weight: 600;
        color: #374151;
        font-size: 0.8rem; // Réduction de la taille (0.875rem -> 0.8rem)
        padding: 0.6rem 0.5rem; // Réduction du padding
      }

      td {
        padding: 0.5rem; // Réduction du padding (0.75rem -> 0.5rem)
        vertical-align: middle;
        border-color: #e5e7eb;
        font-size: 0.8rem; // Réduction de la taille du texte
      }

      tbody tr:hover {
        background-color: rgba($primary-color, 0.05);
      }
    }

    .product-thumbnail {
      width: 35px; // Réduction de la taille (40px -> 35px)
      height: 35px;
      object-fit: cover;
      border-radius: 6px;
      border: 1px solid #e5e7eb;
    }

    .product-name {
      font-weight: 500;
      color: #374151;
      font-size: 0.8rem; // Réduction de la taille (0.875rem -> 0.8rem)
      line-height: 1.2;
    }

    .empty-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      color: #d1d5db;
      margin-bottom: 1rem;
    }
  }
}

// Top Products
.top-product-item {
  padding: 0.75rem 0; // Réduction du padding (1rem -> 0.75rem)
  border-bottom: 1px solid #e5e7eb;

  &:last-child {
    border-bottom: none;
  }

  .rank-badge {
    width: 1.75rem; // Réduction de la taille (2rem -> 1.75rem)
    height: 1.75rem;
    background-color: $primary-color;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.8rem; // Réduction de la taille (0.875rem -> 0.8rem)
    margin-right: 0.75rem; // Réduction de l'espacement
  }

  .product-name {
    font-weight: 500;
    color: #374151;
    font-size: 0.8rem; // Réduction de la taille (0.875rem -> 0.8rem)
    margin-bottom: 0.2rem; // Réduction de l'espacement
  }

  .product-stats {
    margin-bottom: 0.4rem; // Réduction de l'espacement
    font-size: 0.75rem; // Réduction de la taille
  }

  .product-badges {
    .badge {
      font-size: 0.7rem; // Réduction de la taille (0.75rem -> 0.7rem)
      margin-right: 0.25rem;
    }
  }
}

// Badges
.badge {
  &.bg-primary {
    background-color: $primary-color !important;
  }

  &.bg-success {
    background-color: $success-color !important;
  }

  &.bg-warning {
    background-color: $warning-color !important;
  }

  &.bg-danger {
    background-color: $danger-color !important;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .dashboard-header {
    padding: 1.5rem;

    .header-content {
      flex-direction: column;
      align-items: stretch;
      gap: 1rem;
    }

    .welcome-section {
      text-align: center;
      min-width: auto;

      .welcome-title {
        font-size: 1.75rem;
      }

      .welcome-subtitle {
        font-size: 1rem;
      }
    }

    .header-controls {
      justify-content: center;
      gap: 1rem;
    }

    .period-selector {
      flex-direction: column;
      gap: 0.5rem;
      align-items: center;

      .period-toggle-group {
        .period-toggle {
          font-size: 0.75rem;
          padding: 0.4rem 0.8rem;
        }
      }
    }
  }

  .kpi-section {
    .kpi-grid {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
    }

    .modern-card {
      padding: 1.25rem;

      .kpi-header {
        .kpi-icon-wrapper {
          width: 40px;
          height: 40px;
          margin-right: 0.5rem;

          .kpi-icon {
            font-size: 20px;
            width: 20px;
            height: 20px;
          }
        }

        .kpi-title {
          font-size: 0.8rem;
        }
      }

      .kpi-value-section {
        .kpi-value {
          font-size: 1.75rem;
        }
      }
    }
  }

  .section-title {
    font-size: 1.25rem;
  }

  .chart-container {
    height: 250px !important;
  }

  .table-responsive {
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    .period-toggle-group {
      .period-toggle {
        font-size: 0.7rem;
        padding: 0.3rem 0.6rem;
      }
    }

    .refresh-btn {
      font-size: 0.875rem;
      padding: 0.5rem 1rem;
    }
  }
}
