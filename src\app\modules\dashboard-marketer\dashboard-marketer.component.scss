// Variables de couleurs professionnelles Affilink
$primary-color: #664DC9;        // Primaire (violet foncé)
$secondary-color: #A8A4F2;      // Secondaire lavande
$text-dark: #2F2F2F;            // Texte foncé
$text-light: #828282;           // Texte clair
$bg-alt: #F4F3FC;               // Fond alternatif
$border-color: #DCDCE6;         // Bordures
$success-color: #10B981;        // Succès
$error-color: #EF4444;          // Erreur
$warning-color: #f59e0b;        // Attention
$info-color: #3b82f6;           // Information

// Shadows modernes
$card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$card-shadow-hover: 0 8px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 10px -2px rgba(0, 0, 0, 0.04);

// Optimisations globales pour compacité maximale
:host {
  display: block;
  padding: 1rem;
  background: #fafafa;
  min-height: 100vh;

  .mb-5 {
    margin-bottom: 1.5rem !important;
  }

  .mb-4 {
    margin-bottom: 1rem !important;
  }

  .mb-3 {
    margin-bottom: 0.75rem !important;
  }
}

// Dashboard Header moderne et compact
.dashboard-header {
  background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
  border-radius: 16px;
  padding: 1rem 1.5rem;
  margin-bottom: 1.5rem;
  color: white;
  box-shadow: 0 8px 32px rgba($primary-color, 0.3);

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .welcome-section {
    flex: 1;
    min-width: 300px;

    .welcome-title {
      font-size: 1.5rem;
      font-weight: 700;
      margin: 0 0 0.25rem 0;
      color: white;
      line-height: 1.2;
    }

    .welcome-subtitle {
      font-size: 0.9rem;
      margin: 0;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 400;
    }
  }

  .header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .period-selector {
    display: flex;
    align-items: center;
    gap: 0.75rem;

    .period-label {
      font-weight: 500;
      color: rgba(255, 255, 255, 0.9);
      margin: 0;
      font-size: 0.875rem;
    }

    .period-display {
      .period-badge {
        background: rgba(255, 255, 255, 0.25);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 600;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
      }
    }
  }

  .refresh-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-1px);
    }

    &:disabled {
      background: rgba(255, 255, 255, 0.1);
      color: rgba(255, 255, 255, 0.6);
    }

    mat-icon {
      margin-right: 0.5rem;

      &.spinning {
        animation: spin 1s linear infinite;
      }
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// Section Titles compacts
.section-title {
  color: #374151;
  font-weight: 600;
  font-size: 1.25rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;

  mat-icon {
    color: $primary-color;
    font-size: 1.25rem;
    margin-right: 0.5rem;
  }
}

// KPI Section ultra-compacte
.kpi-section {
  margin-bottom: 1.5rem;

  .kpi-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 0.75rem;
    margin-top: 1rem;

    @media (max-width: 1400px) {
      grid-template-columns: repeat(3, 1fr);
    }

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 480px) {
      grid-template-columns: 1fr;
    }
  }

  .kpi-card-wrapper {
    position: relative;
  }

  .modern-card {
    background: white;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: $card-shadow;
    border: 1px solid $border-color;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    height: 140px;

    &:hover {
      transform: translateY(-2px);
      box-shadow: $card-shadow-hover;
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, $primary-color, $secondary-color);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover::before {
      opacity: 1;
    }
  }

  .kpi-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;

    .kpi-icon-wrapper {
      width: 36px;
      height: 36px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 0.75rem;
      transition: transform 0.3s ease;

      .kpi-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }

    .kpi-title {
      font-size: 0.75rem;
      font-weight: 600;
      color: $text-dark;
      margin: 0;
      line-height: 1.3;
    }
  }

  .kpi-value-section {
    margin-bottom: 0.5rem;

    .kpi-value {
      font-size: 1.5rem;
      font-weight: 700;
      margin: 0;
      line-height: 1.1;
      color: $text-dark;
    }
  }

  .kpi-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.7rem;

    .kpi-variation {
      display: flex;
      align-items: center;
      font-weight: 600;
      padding: 0.25rem 0.5rem;
      border-radius: 6px;

      .variation-icon {
        font-size: 12px;
        width: 12px;
        height: 12px;
        margin-right: 0.25rem;
      }

      .variation-value {
        font-weight: 600;
      }

      &.text-success {
        background: rgba($success-color, 0.1);
        color: $success-color;
      }

      &.text-danger {
        background: rgba($error-color, 0.1);
        color: $error-color;
      }
    }

    .variation-label,
    .no-comparison {
      color: $text-light;
      font-weight: 500;
    }
  }

  .kpi-progress {
    margin-top: 0.5rem;
    height: 3px;
    background: $bg-alt;
    border-radius: 2px;
    overflow: hidden;

    .progress-bar {
      height: 100%;
      background: linear-gradient(90deg, $primary-color, $secondary-color);
      border-radius: 2px;
      transition: width 0.8s ease;
    }
  }

  // Couleurs spécifiques par type de carte
  .kpi-primary {
    .kpi-icon-wrapper {
      background: rgba($primary-color, 0.1);
      .kpi-icon { color: $primary-color; }
    }
    .kpi-value { color: $primary-color; }
  }

  .kpi-success {
    .kpi-icon-wrapper {
      background: rgba($success-color, 0.1);
      .kpi-icon { color: $success-color; }
    }
    .kpi-value { color: $success-color; }
  }

  .kpi-warning {
    .kpi-icon-wrapper {
      background: rgba($warning-color, 0.1);
      .kpi-icon { color: $warning-color; }
    }
    .kpi-value { color: $warning-color; }
  }

  .kpi-accent {
    .kpi-icon-wrapper {
      background: rgba($secondary-color, 0.1);
      .kpi-icon { color: $secondary-color; }
    }
    .kpi-value { color: $secondary-color; }
  }

  .kpi-info {
    .kpi-icon-wrapper {
      background: rgba($info-color, 0.1);
      .kpi-icon { color: $info-color; }
    }
    .kpi-value { color: $info-color; }
  }

  .kpi-secondary {
    .kpi-icon-wrapper {
      background: rgba($text-light, 0.1);
      .kpi-icon { color: $text-light; }
    }
    .kpi-value { color: $text-light; }
  }
}

// Skeleton Loader moderne
.skeleton-card {
  .skeleton-loader {
    .skeleton-header {
      display: flex;
      align-items: center;
      margin-bottom: 1rem;

      .skeleton-icon {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        margin-right: 0.75rem;
      }

      .skeleton-title {
        flex: 1;
        height: 0.75rem;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 4px;
      }
    }

    .skeleton-value {
      height: 1.5rem;
      width: 70%;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
      border-radius: 4px;
      margin-bottom: 0.5rem;
    }

    .skeleton-footer {
      .skeleton-variation {
        height: 0.7rem;
        width: 50%;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 4px;
      }
    }
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// Charts Section optimisée
.charts-section {
  margin-bottom: 1.5rem;

  .charts-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1rem;
    margin-top: 1rem;
    align-items: start;

    .chart-card-wrapper {
      position: relative;

      &.full-width {
        grid-column: 1 / -1;
      }
    }

    .evolution-chart {
      grid-row: 1 / 3;

      .modern-chart {
        height: 100%;
        min-height: 380px;

        .chart-content {
          min-height: 320px;
        }
      }
    }

    .right-charts-column {
      display: flex;
      flex-direction: column;
      gap: 1rem;

      .donut-chart,
      .bar-chart {
        flex: 1;
        min-height: 120px;

        .modern-chart {
          height: 100%;
          display: flex;
          flex-direction: column;
        }

        .chart-content {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }

  .modern-chart {
    background: white;
    border-radius: 12px;
    padding: 0.5rem;
    box-shadow: $card-shadow;
    border: 1px solid $border-color;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;

    &:hover {
      transform: translateY(-2px);
      box-shadow: $card-shadow-hover;
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, $primary-color, $secondary-color);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover::before {
      opacity: 1;
    }
  }

  .chart-header {
    margin-bottom: 0.75rem;

    .chart-title {
      font-size: 1rem;
      font-weight: 700;
      color: $text-dark;
      margin: 0 0 0.25rem 0;
      line-height: 1.3;
    }

    .chart-subtitle {
      font-size: 0.75rem;
      color: $text-light;
      margin: 0;
      font-weight: 500;
    }
  }

  .chart-content {
    position: relative;
    flex: 1;
    min-height: 200px;
    display: flex;
    flex-direction: column;

    ::ng-deep {
      .apexcharts-canvas {
        margin: 0 auto;
      }

      .apexcharts-legend {
        justify-content: center !important;
      }

      .apexcharts-tooltip {
        background: rgba(255, 255, 255, 0.95) !important;
        border: 1px solid $border-color !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
        border-radius: 8px !important;
      }

      .apexcharts-tooltip-title {
        background: $bg-alt !important;
        border-bottom: 1px solid $border-color !important;
        font-weight: 600 !important;
        color: $text-dark !important;
      }

      .apexcharts-tooltip-series-group {
        background: transparent !important;
      }

      .apexcharts-tooltip-text-y-label,
      .apexcharts-tooltip-text-y-value {
        color: $text-dark !important;
        font-weight: 500 !important;
      }

      // Lignes continues et lisibles pour le graphique d'évolution
      .apexcharts-line {
        stroke-width: 3px !important;
        stroke-linecap: round !important;
      }

      .apexcharts-area-series {
        opacity: 0.2 !important;
      }

      // Amélioration du donut chart
      .apexcharts-donut {
        transform: scale(1.1);
      }

      .apexcharts-legend {
        font-size: 0.75rem;
        margin-top: 0.5rem;
      }

      // Amélioration du bar chart
      .apexcharts-bar-series {
        stroke-width: 2px;
      }
    }
  }

  // Skeleton loader pour les graphiques
  .skeleton-chart {
    .chart-skeleton {
      .skeleton-header {
        margin-bottom: 1rem;

        .skeleton-title {
          height: 1rem;
          width: 60%;
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200% 100%;
          animation: loading 1.5s infinite;
          border-radius: 4px;
          margin-bottom: 0.5rem;
        }

        .skeleton-subtitle {
          height: 0.75rem;
          width: 40%;
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200% 100%;
          animation: loading 1.5s infinite;
          border-radius: 4px;
        }
      }

      .skeleton-chart-area {
        height: 250px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: $text-light;

        p {
          margin-top: 1rem;
          font-size: 0.875rem;
          font-weight: 500;
        }
      }
    }
  }

  // Responsive pour les graphiques
  @media (max-width: 768px) {
    .charts-grid {
      grid-template-columns: 1fr;
      gap: 1rem;

      .chart-card-wrapper.full-width {
        grid-column: 1;
      }

      .evolution-chart {
        grid-row: auto;

        .modern-chart {
          min-height: 300px;

          .chart-content {
            min-height: 250px;
          }
        }
      }

      .right-charts-column {
        height: auto;

        .donut-chart,
        .bar-chart {
          min-height: 250px;
        }
      }
    }
  }
}

// Tables Section compacte
.tables-section {
  .table-card {
    border-radius: 12px;
    box-shadow: $card-shadow;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: $card-shadow-hover;
    }

    mat-card-header {
      background-color: $bg-alt;
      border-radius: 12px 12px 0 0;
      padding: 1rem;

      mat-card-title {
        color: #374151;
        font-weight: 600;
        font-size: 1rem;
        display: flex;
        align-items: center;

        mat-icon {
          margin-right: 0.5rem;
          color: $primary-color;
        }
      }

      mat-card-subtitle {
        color: #6b7280;
        font-size: 0.8rem;
      }
    }

    mat-card-content {
      padding: 1rem;
    }

    .table-responsive {
      max-height: 280px;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: $primary-color;
        border-radius: 3px;
      }
    }

    .table {
      margin-bottom: 0;

      th {
        background-color: $bg-alt;
        border-top: none;
        font-weight: 600;
        color: #374151;
        font-size: 0.8rem;
        padding: 0.75rem 0.5rem;
        position: sticky;
        top: 0;
        z-index: 10;
      }

      td {
        padding: 0.75rem 0.5rem;
        vertical-align: middle;
        border-color: #e5e7eb;
        font-size: 0.8rem;
      }

      tbody tr {
        transition: background-color 0.2s ease;

        &:hover {
          background-color: rgba($primary-color, 0.05);
        }
      }
    }

    .product-thumbnail {
      width: 40px;
      height: 40px;
      object-fit: cover;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
    }

    .product-name {
      font-weight: 500;
      color: #374151;
      font-size: 0.85rem;
      line-height: 1.2;
    }

    .empty-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      color: #d1d5db;
      margin-bottom: 1rem;
    }
  }
}

// Top Products styling
.top-product-item {
  padding: 0.75rem 0;
  border-bottom: 1px solid #e5e7eb;
  transition: background-color 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: rgba($primary-color, 0.02);
  }

  .rank-badge {
    width: 2rem;
    height: 2rem;
    background: linear-gradient(135deg, $primary-color, $secondary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    margin-right: 0.75rem;
    box-shadow: 0 2px 8px rgba($primary-color, 0.3);
  }

  .product-name {
    font-weight: 500;
    color: #374151;
    font-size: 0.85rem;
    margin-bottom: 0.25rem;
  }

  .product-stats {
    margin-bottom: 0.5rem;
    font-size: 0.75rem;
  }

  .product-badges {
    .badge {
      font-size: 0.7rem;
      margin-right: 0.25rem;
      padding: 0.25rem 0.5rem;
    }
  }
}

// Badges améliorés
.badge {
  &.bg-primary {
    background-color: $primary-color !important;
  }

  &.bg-success {
    background-color: $success-color !important;
  }

  &.bg-warning {
    background-color: $warning-color !important;
  }

  &.bg-danger {
    background-color: $error-color !important;
  }

  &.bg-info {
    background-color: $info-color !important;
  }

  &.bg-light {
    background-color: #f8f9fa !important;
    color: #495057 !important;
  }
}

// Snackbar personnalisés
::ng-deep {
  .success-snackbar {
    background-color: $success-color !important;
    color: white !important;
  }

  .error-snackbar {
    background-color: $error-color !important;
    color: white !important;
  }
}

// Responsive Design optimisé
@media (max-width: 1200px) {
  .dashboard-header {
    .header-content {
      flex-direction: column;
      align-items: stretch;
      gap: 1rem;
    }

    .welcome-section {
      text-align: center;
      min-width: auto;
    }

    .header-controls {
      justify-content: center;
      gap: 1rem;
    }
  }
}

@media (max-width: 768px) {
  :host {
    padding: 0.5rem;
  }

  .dashboard-header {
    padding: 1rem;
    margin-bottom: 1rem;

    .welcome-title {
      font-size: 1.25rem;
    }

    .welcome-subtitle {
      font-size: 0.8rem;
    }
  }

  .section-title {
    font-size: 1.1rem;
  }

  .kpi-section {
    .modern-card {
      height: 120px;
      padding: 0.75rem;

      .kpi-header {
        .kpi-icon-wrapper {
          width: 32px;
          height: 32px;

          .kpi-icon {
            font-size: 16px;
            width: 16px;
            height: 16px;
          }
        }

        .kpi-title {
          font-size: 0.7rem;
        }
      }

      .kpi-value-section {
        .kpi-value {
          font-size: 1.25rem;
        }
      }
    }
  }

  .tables-section {
    .table-responsive {
      font-size: 0.75rem;
    }

    .product-thumbnail {
      width: 35px;
      height: 35px;
    }
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    .refresh-btn {
      font-size: 0.8rem;
      padding: 0.5rem 0.75rem;
    }
  }

  .kpi-section {
    .modern-card {
      height: 110px;
      padding: 0.5rem;

      .kpi-value-section {
        .kpi-value {
          font-size: 1.1rem;
        }
      }
    }
  }
}
