package com.forlivraison.web.dao;

import com.forlivraison.web.entity.Order;
import com.forlivraison.web.entity.User;
import com.forlivraison.web.enums.OrderStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Date;

@Repository
public interface OrderRepository extends JpaRepository<Order, Long> {
    
    List<Order> findByMarketerAndStatusOrderByOrderDateDesc(User marketer, OrderStatus status);
    
    Page<Order> findByMarketerAndStatusOrderByOrderDateDesc(User marketer, OrderStatus status, Pageable pageable);
    
    long countByMarketerAndStatus(User marketer, OrderStatus status);
    
    @Query("SELECT o FROM Order o WHERE o.marketer = :marketer AND o.status = :status AND " +
           "(o.nomDestinataire LIKE %:keyword% OR o.commentaire LIKE %:keyword%)")
    List<Order> findByMarketerAndStatusAndKeyword(@Param("marketer") User marketer, 
                                                 @Param("status") OrderStatus status,
                                                 @Param("keyword") String keyword);

       // Nouvelles méthodes pour l'admin
    Page<Order> findByStatusOrderByOrderDateDesc(OrderStatus status, Pageable pageable);
    
    long countByStatus(OrderStatus status);
    
    @Query("SELECT o FROM Order o WHERE o.status = :status AND " +
           "(o.nomDestinataire LIKE %:keyword% OR o.commentaire LIKE %:keyword% OR " +
           "o.marketer.firstName LIKE %:keyword% OR o.marketer.lastName LIKE %:keyword%)")
    Page<Order> findByStatusAndKeywordOrderByOrderDateDesc(@Param("status") OrderStatus status,
                                                          @Param("keyword") String keyword,
                                                          Pageable pageable);

    // ==================== MÉTHODES POUR LES COMMANDES CONFIRMÉES (ADMIN) ====================

    @Query("SELECT o FROM Order o WHERE o.status IN ('CONFIRMED', 'DELIVERED') " +
           "AND (:status IS NULL OR o.status = :status) " +
           "ORDER BY o.orderDate DESC")
    Page<Order> findConfirmedOrdersWithFilters(@Param("status") OrderStatus status,
                                              Pageable pageable);

    @Query("SELECT o FROM Order o WHERE o.status IN ('CONFIRMED', 'DELIVERED') " +
           "AND (:status IS NULL OR o.status = :status) " +
           "AND (o.nomDestinataire LIKE %:search% OR o.commentaire LIKE %:search% OR " +
           "o.marketer.firstName LIKE %:search% OR o.marketer.lastName LIKE %:search% OR " +
           "o.telephoneDestinataire LIKE %:search%) " +
           "ORDER BY o.orderDate DESC")
    Page<Order> findConfirmedOrdersWithFiltersAndSearch(@Param("status") OrderStatus status,
                                                       @Param("search") String search,
                                                       Pageable pageable);

    @Query("SELECT o FROM Order o WHERE o.status IN ('CONFIRMED', 'DELIVERED') " +
           "AND (:status IS NULL OR o.status = :status) " +
           "ORDER BY o.orderDate DESC")
    List<Order> findConfirmedOrdersForExport(@Param("status") OrderStatus status);

    @Query("SELECT o FROM Order o WHERE o.status IN ('CONFIRMED', 'DELIVERED') " +
           "AND (:status IS NULL OR o.status = :status) " +
           "AND (o.nomDestinataire LIKE %:search% OR o.commentaire LIKE %:search% OR " +
           "o.marketer.firstName LIKE %:search% OR o.marketer.lastName LIKE %:search% OR " +
           "o.telephoneDestinataire LIKE %:search%) " +
           "ORDER BY o.orderDate DESC")
    List<Order> findConfirmedOrdersForExportWithSearch(@Param("status") OrderStatus status,
                                                      @Param("search") String search);

    @Query("SELECT COUNT(o) FROM Order o WHERE o.status IN ('CONFIRMED', 'DELIVERED') " +
           "AND (:status IS NULL OR o.status = :status)")
    long countConfirmedOrdersWithFilters(@Param("status") OrderStatus status);

    // ===== MÉTHODES POUR FILTRAGE PAR FACTURE =====

    @Query("SELECT o FROM Order o WHERE o.facture.id = :factureId " +
           "AND o.status IN ('CONFIRMED', 'DELIVERED') " +
           "AND (:status IS NULL OR o.status = :status) " +
           "ORDER BY o.orderDate DESC")
    Page<Order> findConfirmedOrdersByFacture(@Param("factureId") Long factureId,
                                            @Param("status") OrderStatus status,
                                            Pageable pageable);

    @Query("SELECT o FROM Order o WHERE o.facture.id = :factureId " +
           "AND o.status IN ('CONFIRMED', 'DELIVERED') " +
           "AND (:status IS NULL OR o.status = :status) " +
           "AND (o.nomDestinataire LIKE %:search% OR o.commentaire LIKE %:search% OR " +
           "o.marketer.firstName LIKE %:search% OR o.marketer.lastName LIKE %:search% OR " +
           "o.telephoneDestinataire LIKE %:search%) " +
           "ORDER BY o.orderDate DESC")
    Page<Order> findConfirmedOrdersByFactureWithSearch(@Param("factureId") Long factureId,
                                                      @Param("status") OrderStatus status,
                                                      @Param("search") String search,
                                                      Pageable pageable);

    // ==================== MÉTHODES POUR LES COMMANDES CONFIRMÉES (MARKETER) ====================

    @Query("SELECT o FROM Order o WHERE o.marketer.id = :marketerId " +
           "AND o.status IN ('CONFIRMED', 'DELIVERED') " +
           "AND (:status IS NULL OR o.status = :status) " +
           "AND (:startDate IS NULL OR o.orderDate >= :startDate) " +
           "AND (:endDate IS NULL OR o.orderDate <= :endDate) " +
           "ORDER BY o.orderDate DESC")
    Page<Order> findConfirmedOrdersByMarketerWithFilters(@Param("marketerId") Long marketerId,
                                                        @Param("status") OrderStatus status,
                                                        @Param("startDate") Date startDate,
                                                        @Param("endDate") Date endDate,
                                                        Pageable pageable);

    @Query("SELECT o FROM Order o WHERE o.marketer.id = :marketerId " +
           "AND o.status IN ('CONFIRMED', 'DELIVERED') " +
           "AND (:status IS NULL OR o.status = :status) " +
           "AND (:startDate IS NULL OR o.orderDate >= :startDate) " +
           "AND (:endDate IS NULL OR o.orderDate <= :endDate) " +
           "AND (o.nomDestinataire LIKE %:search% OR o.commentaire LIKE %:search% OR " +
           "o.telephoneDestinataire LIKE %:search%) " +
           "ORDER BY o.orderDate DESC")
    Page<Order> findConfirmedOrdersByMarketerWithFiltersAndSearch(@Param("marketerId") Long marketerId,
                                                                @Param("status") OrderStatus status,
                                                                @Param("search") String search,
                                                                @Param("startDate") Date startDate,
                                                                @Param("endDate") Date endDate,
                                                                Pageable pageable);

    // ===== MÉTHODES POUR FILTRAGE PAR FACTURE (MARKETER) =====

    @Query("SELECT o FROM Order o WHERE o.marketer.id = :marketerId " +
           "AND o.facture.id = :factureId " +
           "AND o.status IN ('CONFIRMED', 'DELIVERED') " +
           "AND (:status IS NULL OR o.status = :status) " +
           "ORDER BY o.orderDate DESC")
    Page<Order> findConfirmedOrdersByMarketerAndFacture(@Param("marketerId") Long marketerId,
                                                       @Param("factureId") Long factureId,
                                                       @Param("status") OrderStatus status,
                                                       Pageable pageable);

    @Query("SELECT o FROM Order o WHERE o.marketer.id = :marketerId " +
           "AND o.facture.id = :factureId " +
           "AND o.status IN ('CONFIRMED', 'DELIVERED') " +
           "AND (:status IS NULL OR o.status = :status) " +
           "AND (o.nomDestinataire LIKE %:search% OR o.commentaire LIKE %:search% OR " +
           "o.telephoneDestinataire LIKE %:search%) " +
           "ORDER BY o.orderDate DESC")
    Page<Order> findConfirmedOrdersByMarketerAndFactureWithSearch(@Param("marketerId") Long marketerId,
                                                                 @Param("factureId") Long factureId,
                                                                 @Param("status") OrderStatus status,
                                                                 @Param("search") String search,
                                                                 Pageable pageable);

    @Query("SELECT o FROM Order o WHERE o.marketer.id = :marketerId " +
           "AND o.status IN ('CONFIRMED', 'DELIVERED') " +
           "AND (:status IS NULL OR o.status = :status) " +
           "AND (:startDate IS NULL OR o.orderDate >= :startDate) " +
           "AND (:endDate IS NULL OR o.orderDate <= :endDate) " +
           "ORDER BY o.orderDate DESC")
    List<Order> findConfirmedOrdersByMarketerForExport(@Param("marketerId") Long marketerId,
                                                      @Param("status") OrderStatus status,
                                                      @Param("startDate") Date startDate,
                                                      @Param("endDate") Date endDate);

    @Query("SELECT o FROM Order o WHERE o.marketer.id = :marketerId " +
           "AND o.status IN ('CONFIRMED', 'DELIVERED') " +
           "AND (:status IS NULL OR o.status = :status) " +
           "AND (:startDate IS NULL OR o.orderDate >= :startDate) " +
           "AND (:endDate IS NULL OR o.orderDate <= :endDate) " +
           "AND (o.nomDestinataire LIKE %:search% OR o.commentaire LIKE %:search% OR " +
           "o.telephoneDestinataire LIKE %:search%) " +
           "ORDER BY o.orderDate DESC")
    List<Order> findConfirmedOrdersByMarketerForExportWithSearch(@Param("marketerId") Long marketerId,
                                                               @Param("status") OrderStatus status,
                                                               @Param("search") String search,
                                                               @Param("startDate") Date startDate,
                                                               @Param("endDate") Date endDate);

    @Query("SELECT COUNT(o) FROM Order o WHERE o.marketer.id = :marketerId " +
           "AND o.status IN ('CONFIRMED', 'DELIVERED') " +
           "AND (:status IS NULL OR o.status = :status) " +
           "AND (:startDate IS NULL OR o.orderDate >= :startDate) " +
           "AND (:endDate IS NULL OR o.orderDate <= :endDate)")
    long countConfirmedOrdersByMarketerWithFilters(@Param("marketerId") Long marketerId,
                                                  @Param("status") OrderStatus status,
                                                  @Param("startDate") Date startDate,
                                                  @Param("endDate") Date endDate);

    // ==================== MÉTHODES POUR LES STATISTIQUES (MARKETER) ====================

    @Query("SELECT COUNT(o) FROM Order o WHERE o.marketer.id = :marketerId " +
           "AND o.status IN ('CONFIRMED', 'DELIVERED')")
    long countConfirmedOrdersByMarketer(@Param("marketerId") Long marketerId);

    @Query("SELECT COUNT(o) FROM Order o WHERE o.marketer.id = :marketerId " +
           "AND o.status = :status")
    long countConfirmedOrdersByMarketerAndStatus(@Param("marketerId") Long marketerId,
                                                @Param("status") OrderStatus status);

    @Query("SELECT SUM(o.totalAmount) FROM Order o " +
           "WHERE o.marketer.id = :marketerId " +
           "AND o.status IN ('CONFIRMED', 'DELIVERED')")
    BigDecimal sumRevenueByMarketer(@Param("marketerId") Long marketerId);

    @Query("SELECT COUNT(o) FROM Order o WHERE o.marketer.id = :marketerId " +
           "AND o.status IN ('CONFIRMED', 'DELIVERED')")
    long countConfirmedOrdersByMarketerAndDateRange(@Param("marketerId") Long marketerId);

    @Query("SELECT SUM(o.totalAmount) FROM Order o " +
           "WHERE o.marketer.id = :marketerId " +
           "AND o.status IN ('CONFIRMED', 'DELIVERED')")
    BigDecimal sumRevenueByMarketerAndDateRange(@Param("marketerId") Long marketerId);

    // ==================== NOUVELLES MÉTHODES POUR LE DASHBOARD MARKETER ====================

    // Compter les commandes par marketer et statut
    @Query("SELECT COUNT(o) FROM Order o WHERE o.marketer.id = :marketerId AND o.status = :status")
    Long countByMarketerIdAndStatus(@Param("marketerId") Long marketerId, @Param("status") OrderStatus status);

    // Compter les commandes par marketer, statut et plage de dates (LocalDateTime)
    @Query("SELECT COUNT(o) FROM Order o WHERE o.marketer.id = :marketerId " +
           "AND o.status = :status " +
           "AND o.orderDate BETWEEN :startDate AND :endDate")
    Long countByMarketerIdAndStatusAndLocalDateTimeRange(@Param("marketerId") Long marketerId,
                                                        @Param("status") OrderStatus status,
                                                        @Param("startDate") LocalDateTime startDate,
                                                        @Param("endDate") LocalDateTime endDate);

    // Compter toutes les commandes par marketer dans une plage de dates
    @Query("SELECT COUNT(o) FROM Order o WHERE o.marketer.id = :marketerId " +
           "AND o.orderDate BETWEEN :startDate AND :endDate")
    Long countByMarketerIdAndDateRange(@Param("marketerId") Long marketerId,
                                      @Param("startDate") LocalDateTime startDate,
                                      @Param("endDate") LocalDateTime endDate);

    // Compter les commandes par marketer avec plusieurs statuts et plage de dates
    @Query("SELECT COUNT(o) FROM Order o WHERE o.marketer.id = :marketerId " +
           "AND o.status IN :statuses " +
           "AND o.orderDate BETWEEN :startDate AND :endDate")
    Long countByMarketerIdAndStatusInAndDateRange(@Param("marketerId") Long marketerId,
                                                 @Param("statuses") List<OrderStatus> statuses,
                                                 @Param("startDate") LocalDateTime startDate,
                                                 @Param("endDate") LocalDateTime endDate);

    // Somme du montant total par marketer et plage de dates
    @Query("SELECT COALESCE(SUM(o.totalAmount), 0) FROM Order o WHERE o.marketer.id = :marketerId " +
           "AND o.orderDate BETWEEN :startDate AND :endDate")
    BigDecimal sumTotalAmountByMarketerAndDateRange(@Param("marketerId") Long marketerId,
                                                   @Param("startDate") LocalDateTime startDate,
                                                   @Param("endDate") LocalDateTime endDate);

    // Récupérer les dernières commandes d'un marketer
    @Query("SELECT o FROM Order o WHERE o.marketer.id = :marketerId " +
           "ORDER BY o.orderDate DESC")
    List<Order> findRecentOrdersByMarketer(@Param("marketerId") Long marketerId, Pageable pageable);

    // Évolution des commandes par jour pour un marketer (30 derniers jours)
    @Query("SELECT DATE(o.orderDate) as date, o.status, COUNT(o) as count " +
           "FROM Order o WHERE o.marketer.id = :marketerId " +
           "AND o.orderDate >= :startDate " +
           "GROUP BY DATE(o.orderDate), o.status " +
           "ORDER BY DATE(o.orderDate)")
    List<Object[]> findOrderEvolutionByMarketer(@Param("marketerId") Long marketerId,
                                               @Param("startDate") LocalDateTime startDate);


}
