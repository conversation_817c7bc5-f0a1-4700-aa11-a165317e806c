package com.forlivraison.web.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * DTO pour les produits les plus performants
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Informations d'un produit performant")
public class TopProductDto {

    @Schema(description = "ID du produit", example = "501")
    private Long productId;

    @Schema(description = "Nom du produit", example = "MacBook Pro M3")
    private String productName;

    @Schema(description = "URL de l'image du produit")
    private String productImage;

    @Schema(description = "URL de la miniature du produit")
    private String productThumbnail;

    // Prix et promotions
    @Schema(description = "Prix normal du produit", example = "2499.99")
    private Double price;

    @Schema(description = "Prix promotionnel", example = "2199.99")
    private Double promotionPrice;

    @Schema(description = "Pourcentage de promotion", example = "12.0")
    private Double promotionPercentage;

    @Schema(description = "Produit en WOW Price", example = "true")
    private Boolean isWowPrice;

    // Statistiques de performance
    @Schema(description = "Nombre total de commandes pour ce produit", example = "25")
    private Integer totalCommandes;

    @Schema(description = "Quantité totale vendue", example = "30")
    private Integer totalQuantiteVendue;

    @Schema(description = "Chiffre d'affaires généré", example = "62499.75")
    private Double revenuGenere;

    @Schema(description = "Commission totale générée", example = "6249.98")
    private Double commissionTotale;

    // Taux de performance
    @Schema(description = "Taux de conversion pour ce produit (%)", example = "78.5")
    private Double tauxConversion;

    // Période de sélection
    @Schema(description = "Date de sélection du produit", example = "2024-11-01")
    private String dateSelection;

    @Schema(description = "Nombre de jours depuis la sélection", example = "45")
    private Integer joursDepuisSelection;
}
