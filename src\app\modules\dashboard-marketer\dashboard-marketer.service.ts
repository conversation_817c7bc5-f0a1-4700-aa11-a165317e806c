import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { TokenStorageService } from '../login/authentication/token-storage.service';

import {
  MarketerDashboardStats,
  MarketerDashboardCharts,
  RecentOrder,
  TopProduct
} from './dashboard-marketer.model';

@Injectable({
  providedIn: 'root'
})
export class DashboardMarketerService {

  private baseUrl = environment.baseUrl;

  constructor(
    private http: HttpClient,
    private tokenStorage: TokenStorageService
  ) {}

  private getHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Authorization': `Bearer ${this.tokenStorage.getToken()}`
    });
  }

  /**
   * Récupère les statistiques principales pour le dashboard
   */
  getDashboardStats(): Observable<MarketerDashboardStats> {
    return this.http.get<MarketerDashboardStats>(
      `${this.baseUrl}/api/marketer/dashboard/stats`,
      { headers: this.getHeaders() }
    );
  }

  /**
   * Récupère les données pour les graphiques
   */
  getDashboardCharts(): Observable<MarketerDashboardCharts> {
    return this.http.get<MarketerDashboardCharts>(
      `${this.baseUrl}/api/marketer/dashboard/charts`,
      { headers: this.getHeaders() }
    );
  }

  /**
   * Récupère les dernières commandes
   */
  getRecentOrders(limit: number = 10): Observable<RecentOrder[]> {
    return this.http.get<RecentOrder[]>(
      `${this.baseUrl}/api/marketer/dashboard/recent-orders?limit=${limit}`,
      { headers: this.getHeaders() }
    );
  }

  /**
   * Récupère le top des produits performants
   */
  getTopProducts(limit: number = 5): Observable<TopProduct[]> {
    return this.http.get<TopProduct[]>(
      `${this.baseUrl}/api/marketer/dashboard/top-products?limit=${limit}`,
      { headers: this.getHeaders() }
    );
  }

  /**
   * Récupère les informations du marketer connecté
   */
  getMarketerInfo(): Observable<any> {
    return this.http.get<any>(
      `${this.baseUrl}/api/marketer/dashboard/profile`,
      { headers: this.getHeaders() }
    );
  }

  /**
   * Test de santé de l'API
   */
  healthCheck(): Observable<string> {
    return this.http.get<string>(
      `${this.baseUrl}/api/marketer/dashboard/health`,
      { headers: this.getHeaders() }
    );
  }
}
