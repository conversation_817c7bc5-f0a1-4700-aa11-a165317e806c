package com.forlivraison.web.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MarketerDashboardStatsDto {
    
    // 1.1 Produits sélectionnés
    private Long totalProduitsActifs;
    private Long totalProduitsWowPrice;
    private Double variationProduitsVsPrecedent; // en pourcentage
    
    // 1.2 Commandes non confirmées
    private Long commandesEnAttente;
    private Double variationCommandesEnAttenteVsPrecedent;
    
    // 1.3 Commandes confirmées
    private Long commandesConfirmeesLivrees;
    private Long commandesConfirmeesEnCours;
    private Long totalCommandesConfirmees;
    private Double variationCommandesConfirmeesVsPrecedent;
    
    // 1.4 Taux de conversion
    private Double tauxConversion; // (Commandes confirmées / Commandes soumises) * 100
    private Double variationTauxConversionVsPrecedent;
    
    // Données additionnelles pour les KPIs
    private BigDecimal chiffreAffairesMois; // CA du mois en cours
    private BigDecimal commissionsEnAttente; // Commissions non facturées
    private BigDecimal dernierPaiement; // Montant du dernier paiement reçu

    // 1.5 Données des factures
    private Long facturesDisponibles; // Nombre total de factures
    private BigDecimal montantTotalFactures; // Montant total des factures
    private Long facturesPayees; // Nombre de factures payées
    private Long facturesEnAttente; // Nombre de factures en attente

    // Période de référence
    private String periodeActuelle; // ex: "Décembre 2024"
    private String periodePrecedente; // ex: "Novembre 2024"
}
