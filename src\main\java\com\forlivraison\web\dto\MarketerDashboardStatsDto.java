package com.forlivraison.web.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * DTO pour les statistiques principales du dashboard marketer
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Statistiques principales du dashboard marketer")
public class MarketerDashboardStatsDto {

    // 1.1 Produits sélectionnés
    @Schema(description = "Nombre total de produits actifs", example = "25")
    private Integer totalProduitsActifs;

    @Schema(description = "Nombre de produits avec WOW Price", example = "8")
    private Integer totalProduitsWowPrice;

    @Schema(description = "Variation des produits vs période précédente (%)", example = "12.5")
    private Double variationProduitsVsPrecedent;

    // 1.2 Commandes non confirmées
    @Schema(description = "Nombre de commandes en attente", example = "15")
    private Integer commandesEnAttente;

    @Schema(description = "Variation des commandes en attente vs période précédente (%)", example = "-5.2")
    private Double variationCommandesEnAttenteVsPrecedent;

    // 1.3 Commandes confirmées
    @Schema(description = "Nombre de commandes confirmées et livrées", example = "42")
    private Integer commandesConfirmeesLivrees;

    @Schema(description = "Nombre de commandes confirmées en cours", example = "18")
    private Integer commandesConfirmeesEnCours;

    @Schema(description = "Nombre total de commandes confirmées", example = "60")
    private Integer totalCommandesConfirmees;

    @Schema(description = "Variation des commandes confirmées vs période précédente (%)", example = "18.7")
    private Double variationCommandesConfirmeesVsPrecedent;

    // 1.4 Taux de conversion
    @Schema(description = "Taux de conversion (commandes confirmées / soumises) * 100", example = "75.5")
    private Double tauxConversion;

    @Schema(description = "Variation du taux de conversion vs période précédente (%)", example = "3.2")
    private Double variationTauxConversionVsPrecedent;

    // Données additionnelles pour les KPIs
    @Schema(description = "Chiffre d'affaires du mois en cours", example = "12500.50")
    private Double chiffreAffairesMois;

    @Schema(description = "Montant des commissions en attente", example = "1250.75")
    private Double commissionsEnAttente;

    @Schema(description = "Montant du dernier paiement reçu", example = "850.25")
    private Double dernierPaiement;

    @Schema(description = "Total des commissions générées (toutes statuts)", example = "9726.50")
    private Double totalCommissionsGenerees;

    // 1.5 Données des factures
    @Schema(description = "Nombre total de factures disponibles", example = "8")
    private Integer facturesDisponibles;

    @Schema(description = "Montant total des factures", example = "5420.80")
    private Double montantTotalFactures;

    @Schema(description = "Nombre de factures payées", example = "5")
    private Integer facturesPayees;

    @Schema(description = "Nombre de factures en attente", example = "3")
    private Integer facturesEnAttenteFacture;

    // Période de référence
    @Schema(description = "Période actuelle", example = "Décembre 2024")
    private String periodeActuelle;

    @Schema(description = "Période précédente", example = "Novembre 2024")
    private String periodePrecedente;
}
