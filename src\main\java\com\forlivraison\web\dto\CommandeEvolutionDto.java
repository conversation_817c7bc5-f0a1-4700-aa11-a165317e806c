package com.forlivraison.web.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * DTO pour l'évolution des commandes par jour
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Évolution des commandes pour une date donnée")
public class CommandeEvolutionDto {

    @Schema(description = "Date au format YYYY-MM-DD", example = "2024-12-15")
    private String date;

    @Schema(description = "Nombre de commandes confirmées", example = "5")
    private Integer commandesConfirmees;

    @Schema(description = "Nombre de commandes rejetées", example = "2")
    private Integer commandesRejetees;

    @Schema(description = "Nombre de commandes en attente", example = "3")
    private Integer commandesEnAttente;
}
