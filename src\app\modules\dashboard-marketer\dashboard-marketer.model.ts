export interface MarketerDashboardStats {
  // 1.1 Produits sélectionnés
  totalProduitsActifs: number
  totalProduitsWowPrice: number
  variationProduitsVsPrecedent: number // en pourcentage

  // 1.2 Commandes non confirmées
  commandesEnAttente: number
  variationCommandesEnAttenteVsPrecedent: number

  // 1.3 Commandes confirmées
  commandesConfirmeesLivrees: number
  commandesConfirmeesEnCours: number
  totalCommandesConfirmees: number
  variationCommandesConfirmeesVsPrecedent: number

  // 1.4 Taux de conversion
  tauxConversion: number // (Commandes confirmées / Commandes soumises) * 100
  variationTauxConversionVsPrecedent: number

  // Données additionnelles pour les KPIs
  chiffreAffairesMois: number // CA du mois en cours
  commissionsEnAttente: number // Commissions non facturées
  dernierPaiement: number // Montant du dernier paiement reçu
  totalCommissionsGenerees: number // Total des commissions générées (toutes statuts)

  // 1.5 Données des factures
  facturesDisponibles: number // Nombre total de factures
  montantTotalFactures: number // Montant total des factures
  facturesPayees: number // Nombre de factures payées
  facturesEnAttenteFacture: number // Nombre de factures en attente

  // Période de référence
  periodeActuelle: string // ex: "Décembre 2024"
  periodePrecedente: string // ex: "Novembre 2024"
}

export interface CommandeEvolution {
  date: string
  commandesConfirmees: number
  commandesRejetees: number
  commandesEnAttente: number
}

export interface ProductCategory {
  categorie: string
  totalProduits: number
  produitsWowPrice: number
  pourcentage: number
  couleur: string // Pour le pie chart
}

export interface MarketerDashboardCharts {
  // 2.1 Évolution des commandes (30 derniers jours)
  evolutionCommandes: CommandeEvolution[]

  // 2.2 Répartition des produits par catégorie
  repartitionProduits: ProductCategory[]
}

export interface RecentOrder {
  id: number
  orderDate: string
  status: string
  statusLabel: string
  statusColor: string

  // Informations produit principal (premier item)
  productName: string
  productImage: string
  productThumbnail: string
  totalQuantity: number // Somme des quantités de tous les items

  // Informations commande
  totalAmount: number
  customerName: string
  customerCity: string

  // Informations de livraison
  deliveryDate?: string
  trackingNumber?: string
}

export interface TopProduct {
  productId: number
  productName: string
  productImage: string
  productThumbnail: string

  // Prix et promotions
  price: number
  promotionPrice?: number
  promotionPercentage?: number
  isWowPrice: boolean

  // Statistiques de performance
  totalCommandes: number // Nombre total de commandes pour ce produit
  totalQuantiteVendue: number // Quantité totale vendue
  revenuGenere: number // Chiffre d'affaires généré
  commissionTotale: number // Commission totale générée

  // Taux de performance
  tauxCommande: number // Pourcentage de commandes par rapport aux autres produits
  tauxConversion: number // Taux de conversion spécifique à ce produit

  // Période de sélection
  dateSelection: string // Date de sélection du produit par le marketer
  joursDepuisSelection: number // Nombre de jours depuis la sélection
}

// Interface pour les cartes KPI
export interface KpiCard {
  title: string
  value: number | string
  variation?: number
  variationLabel?: string
  icon: string
  color: string
  format?: "number" | "currency" | "percentage"
  showProgress?: boolean
}

// Interface pour les informations du marketer
export interface MarketerInfo {
  id: number
  firstName: string
  lastName: string
  email: string
  telephone?: string
  subscriptionType?: string
  status?: string
}
