<!-- Header du Dashboard -->
<div class="dashboard-header">
  <div class="header-content">
    <!-- Message de bienvenue -->
    <div class="welcome-section">
      <h1 class="welcome-title">
        Bienvenue Marketer {{ marketerFullName || marketerName || 'Invité' }} 👋
      </h1>
      <p class="welcome-subtitle">
        Voici un aperçu de vos performances {{ selectedPeriodLabel }}
      </p>
    </div>

    <!-- Contrôles -->
    <div class="header-controls">
      <!-- Sélecteur de période -->
      <div class="period-selector">
        <label class="period-label">Période :</label>
        <mat-button-toggle-group
          [(value)]="selectedPeriod"
          (change)="onPeriodChange($event)"
          class="period-toggle-group">
          <mat-button-toggle value="week" class="period-toggle">
            Cette semaine
          </mat-button-toggle>
          <mat-button-toggle value="14days" class="period-toggle">
            14 derniers jours
          </mat-button-toggle>
          <mat-button-toggle value="month" class="period-toggle">
            Ce mois
          </mat-button-toggle>
        </mat-button-toggle-group>
      </div>

      <!-- Bouton actualiser supprimé - actualisation automatique -->
    </div>
  </div>
</div>

<!-- Section 1: Cartes KPI Optimisées -->
<div class="kpi-section mb-5">
  <h2 class="section-title">
    <mat-icon class="me-2">analytics</mat-icon>
    Statistiques Clés
  </h2>

  <div class="kpi-grid" *ngIf="!isLoadingStats && kpiCards.length > 0">
    <div class="kpi-card-wrapper" *ngFor="let card of kpiCards">
      <div class="kpi-card modern-card" [ngClass]="'kpi-' + card.color">
        <!-- Icône et titre -->
        <div class="kpi-header">
          <div class="kpi-icon-wrapper">
            <mat-icon class="kpi-icon">{{ card.icon }}</mat-icon>
          </div>
          <h6 class="kpi-title">{{ card.title }}</h6>
        </div>

        <!-- Valeur principale -->
        <div class="kpi-value-section">
          <h2 class="kpi-value">{{ formatValue(card.value, card.format) }}</h2>
        </div>

        <!-- Variation et tendance -->
        <div class="kpi-footer" *ngIf="card.variation !== undefined; else noVariation">
          <div class="kpi-variation" [ngClass]="getVariationClass(card.variation)">
            <mat-icon class="variation-icon">{{ getVariationIcon(card.variation) }}</mat-icon>
            <span class="variation-value">{{ formatValue(Math.abs(card.variation), 'percentage') }}</span>
          </div>
          <span class="variation-label">{{ card.variationLabel }}</span>
        </div>

        <ng-template #noVariation>
          <div class="kpi-footer">
            <span class="no-comparison">{{ selectedPeriodLabel }}</span>
          </div>
        </ng-template>

        <!-- Barre de progression pour certaines cartes -->
        <div class="kpi-progress" *ngIf="card.showProgress">
          <div class="progress-bar" [style.width.%]="getProgressPercentage(card)"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading KPI -->
  <div class="kpi-grid" *ngIf="isLoadingStats">
    <div class="kpi-card-wrapper" *ngFor="let i of [1,2,3,4,5,6]">
      <div class="kpi-card modern-card skeleton-card">
        <div class="skeleton-loader">
          <div class="skeleton-header">
            <div class="skeleton-icon"></div>
            <div class="skeleton-title"></div>
          </div>
          <div class="skeleton-value"></div>
          <div class="skeleton-footer">
            <div class="skeleton-variation"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Section 2: Graphiques -->
<div class="charts-section mb-5">
  <h2 class="section-title">
    <mat-icon class="me-2">show_chart</mat-icon>
    Graphiques de Performance
  </h2>

  <div class="charts-grid" *ngIf="!isLoadingCharts">
    <!-- 1. Évolution des commandes (14 jours) - À gauche -->
    <div class="chart-card-wrapper evolution-chart">
      <div class="chart-card modern-chart">
        <div class="chart-header">
          <h3 class="chart-title">📈 Évolution des commandes</h3>
          <p class="chart-subtitle">14 derniers jours glissants</p>
        </div>
        <div class="chart-content">
          <apx-chart
            #lineChart
            [series]="lineChartOptions.series!"
            [chart]="lineChartOptions.chart!"
            [xaxis]="lineChartOptions.xaxis!"
            [yaxis]="lineChartOptions.yaxis!"
            [stroke]="lineChartOptions.stroke!"
            [dataLabels]="lineChartOptions.dataLabels!"
            [tooltip]="lineChartOptions.tooltip!"
            [legend]="lineChartOptions.legend!"
            [fill]="lineChartOptions.fill!"
            [colors]="lineChartOptions.colors!"
            [responsive]="lineChartOptions.responsive!">
          </apx-chart>
        </div>
      </div>
    </div>

    <!-- Colonne droite avec 2 graphiques empilés -->
    <div class="right-charts-column">
      <!-- 2. Répartition des produits WOW Price - En haut à droite -->
      <div class="chart-card-wrapper donut-chart">
        <div class="chart-card modern-chart">
          <div class="chart-header">
            <h3 class="chart-title">Répartition WOW Price</h3>
            <p class="chart-subtitle">% des produits par type</p>
          </div>
          <div class="chart-content">
            <apx-chart
              #donutChart
              [series]="donutChartOptions.series!"
              [chart]="donutChartOptions.chart!"
              [labels]="donutChartOptions.labels!"
              [dataLabels]="donutChartOptions.dataLabels!"
              [plotOptions]="donutChartOptions.plotOptions!"
              [legend]="donutChartOptions.legend!"
              [tooltip]="donutChartOptions.tooltip!"
              [colors]="donutChartOptions.colors!"
              [responsive]="donutChartOptions.responsive!">
            </apx-chart>
          </div>
        </div>
      </div>

      <!-- 3. Top produits performants - En bas à droite -->
      <div class="chart-card-wrapper bar-chart">
        <div class="chart-card modern-chart">
          <div class="chart-header">
            <h3 class="chart-title">🏆 Top 5 produits</h3>
            <p class="chart-subtitle">Classés par commandes</p>
          </div>
          <div class="chart-content">
            <apx-chart
              #barChart
              [series]="barChartOptions.series!"
              [chart]="barChartOptions.chart!"
              [xaxis]="barChartOptions.xaxis!"
              [yaxis]="barChartOptions.yaxis!"
              [plotOptions]="barChartOptions.plotOptions!"
              [dataLabels]="barChartOptions.dataLabels!"
              [tooltip]="barChartOptions.tooltip!"
              [fill]="barChartOptions.fill!"
              [colors]="barChartOptions.colors!"
              [responsive]="barChartOptions.responsive!">
            </apx-chart>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading Charts -->
  <div class="charts-grid" *ngIf="isLoadingCharts">
    <div class="chart-card-wrapper" *ngFor="let i of [1,2,3]">
      <div class="chart-card modern-chart skeleton-chart">
        <div class="chart-skeleton">
          <div class="skeleton-header">
            <div class="skeleton-title"></div>
            <div class="skeleton-subtitle"></div>
          </div>
          <div class="skeleton-chart-area">
            <mat-spinner diameter="50"></mat-spinner>
            <p>Chargement des graphiques...</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Section 3: Tableaux -->
<div class="tables-section">
  <div class="row">
    <!-- Dernières commandes -->
    <div class="col-lg-7 mb-4">
      <mat-card class="table-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon class="me-2">receipt_long</mat-icon>
            Dernières commandes
          </mat-card-title>
          <mat-card-subtitle>10 commandes les plus récentes</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div *ngIf="!isLoadingRecentOrders && recentOrders.length > 0" class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Produit</th>
                  <th>Quantité</th>
                  <th>Statut</th>
                  <th>Date</th>
                  <th>Montant</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let order of recentOrders">
                  <td>
                    <div class="d-flex align-items-center">
                      <img
                        [src]="order.productThumbnail || order.productImage"
                        [alt]="order.productName"
                        class="product-thumbnail me-2"
                        onerror="this.src='assets/images/no-image.png'">
                      <div>
                        <div class="product-name">{{ order.productName }}</div>
                        <small class="text-muted">{{ order.customerCity }}</small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <span class="badge bg-light text-dark">{{ order.totalQuantity }}</span>
                  </td>
                  <td>
                    <span class="badge" [ngClass]="'bg-' + order.statusColor">
                      {{ order.statusLabel }}
                    </span>
                  </td>
                  <td>
                    <small>{{ order.orderDate | date:'dd/MM/yyyy' }}</small>
                  </td>
                  <td>
                    <strong>{{ formatValue(order.totalAmount, 'currency') }}</strong>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <div *ngIf="isLoadingRecentOrders" class="text-center py-4">
            <mat-spinner diameter="40"></mat-spinner>
            <p class="mt-3">Chargement des commandes...</p>
          </div>

          <div *ngIf="!isLoadingRecentOrders && recentOrders.length === 0" class="text-center py-4">
            <mat-icon class="empty-icon">inbox</mat-icon>
            <p class="text-muted">Aucune commande récente</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Top 5 produits -->
    <div class="col-lg-5 mb-4">
      <mat-card class="table-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon class="me-2">star</mat-icon>
            Top 5 produits
          </mat-card-title>
          <mat-card-subtitle>Classés par performance</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div *ngIf="!isLoadingTopProducts && topProducts.length > 0">
            <div class="top-product-item" *ngFor="let product of topProducts; let i = index">
              <div class="d-flex align-items-center">
                <div class="rank-badge">{{ i + 1 }}</div>
                <img
                  [src]="product.productThumbnail || product.productImage"
                  [alt]="product.productName"
                  class="product-thumbnail me-3"
                  onerror="this.src='assets/images/no-image.png'">
                <div class="flex-grow-1">
                  <div class="product-name">{{ product.productName }}</div>
                  <div class="product-stats">
                    <small class="text-muted">
                      {{ product.totalCommandes }} commandes •
                      {{ formatValue(product.revenuGenere, 'currency') }}
                    </small>
                  </div>
                  <div class="product-badges mt-1">
                    <span class="badge bg-primary" *ngIf="product.isWowPrice">WOW Price</span>
                    <span class="badge bg-success">{{ formatValue(product.tauxConversion, 'percentage') }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div *ngIf="isLoadingTopProducts" class="text-center py-4">
            <mat-spinner diameter="40"></mat-spinner>
            <p class="mt-3">Chargement des produits...</p>
          </div>

          <div *ngIf="!isLoadingTopProducts && topProducts.length === 0" class="text-center py-4">
            <mat-icon class="empty-icon">inventory_2</mat-icon>
            <p class="text-muted">Aucun produit trouvé</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>
