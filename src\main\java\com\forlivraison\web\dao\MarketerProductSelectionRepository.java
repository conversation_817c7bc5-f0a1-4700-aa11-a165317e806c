package com.forlivraison.web.dao;

import com.forlivraison.web.entity.MarketerProductSelection;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface MarketerProductSelectionRepository extends JpaRepository<MarketerProductSelection, Long> {

    /**
     * BE-003: Récupération de la sélection actuelle du marketer (exclut les produits en attente de suppression)
     */
    @Query("SELECT mps FROM MarketerProductSelection mps " +
           "JOIN FETCH mps.product p " +
           "WHERE mps.user.id = :userId " +
           "AND (mps.pendingDeletion = false OR mps.pendingDeletion IS NULL) " +
           "ORDER BY mps.selectionDate DESC")
    List<MarketerProductSelection> findByUserIdWithProduct(@Param("userId") Long userId);

    /**
     * Récupération de tous les produits y compris ceux en attente de suppression
     */
    @Query("SELECT mps FROM MarketerProductSelection mps " +
           "JOIN FETCH mps.product p " +
           "WHERE mps.user.id = :userId " +
           "ORDER BY mps.selectionDate DESC")
    List<MarketerProductSelection> findAllByUserIdWithProduct(@Param("userId") Long userId);

    /**
     * BE-003: Suppression définitive d'un produit de la sélection
     */
    @Modifying
    @Query("DELETE FROM MarketerProductSelection mps WHERE mps.user.id = :userId AND mps.product.id = :productId")
    int deleteByUserIdAndProductId(@Param("userId") Long userId, @Param("productId") Long productId);

    /**
     * Marquer un produit pour suppression différée
     */
    @Modifying
    @Query("UPDATE MarketerProductSelection mps SET mps.pendingDeletion = true, mps.deletionScheduledAt = :scheduledTime " +
           "WHERE mps.user.id = :userId AND mps.product.id = :productId")
    int markForDeletion(@Param("userId") Long userId, @Param("productId") Long productId, @Param("scheduledTime") LocalDateTime scheduledTime);

    /**
     * Annuler la suppression différée
     */
    @Modifying
    @Query("UPDATE MarketerProductSelection mps SET mps.pendingDeletion = false, mps.deletionScheduledAt = null " +
           "WHERE mps.user.id = :userId AND mps.product.id = :productId")
    int cancelDeletion(@Param("userId") Long userId, @Param("productId") Long productId);

    /**
     * Trouver les produits dont la suppression a expiré
     */
    @Query("SELECT mps FROM MarketerProductSelection mps " +
           "WHERE mps.pendingDeletion = true " +
           "AND mps.deletionScheduledAt IS NOT NULL " +
           "AND mps.deletionScheduledAt < :currentTime")
    List<MarketerProductSelection> findExpiredDeletions(@Param("currentTime") LocalDateTime currentTime);

    /**
     * BE-002: Supprime toute ancienne sélection avant d'enregistrer la nouvelle
     */
    @Modifying
    @Query("DELETE FROM MarketerProductSelection mps WHERE mps.user.id = :userId")
    int deleteAllByUserId(@Param("userId") Long userId);

    /**
     * Vérifie si une sélection existe pour ce marketer et ce produit
     */
    Optional<MarketerProductSelection> findByUserIdAndProductId(@Param("userId") Long userId,
                                                               @Param("productId") Long productId);

    // ==================== NOUVELLES MÉTHODES POUR LE DASHBOARD MARKETER ====================

    /**
     * Compte le nombre total de produits actifs sélectionnés par un marketer
     */
    @Query("SELECT COUNT(mps) FROM MarketerProductSelection mps " +
           "WHERE mps.user.id = :marketerId " +
           "AND (mps.pendingDeletion = false OR mps.pendingDeletion IS NULL)")
    Long countActiveProductsByMarketer(@Param("marketerId") Long marketerId);

    /**
     * Compte le nombre de produits WOW Price sélectionnés par un marketer
     * (produits avec promotion > 0)
     */
    @Query("SELECT COUNT(mps) FROM MarketerProductSelection mps " +
           "JOIN mps.product p " +
           "WHERE mps.user.id = :marketerId " +
           "AND (mps.pendingDeletion = false OR mps.pendingDeletion IS NULL) " +
           "AND p.promotion IS NOT NULL AND p.promotion > 0")
    Long countWowPriceProductsByMarketer(@Param("marketerId") Long marketerId);

    /**
     * Récupère les produits les plus performants d'un marketer avec leurs statistiques
     */
    @Query("SELECT p.id, p.name, p.imageUrl, p.thumbnailUrl, p.price, p.promotionPrice, " +
           "p.promotion, CASE WHEN p.promotion IS NOT NULL AND p.promotion > 0 THEN true ELSE false END as isWowPrice, " +
           "COALESCE(COUNT(oi.id), 0) as totalCommandes, " +
           "COALESCE(SUM(oi.quantity), 0) as totalQuantite, " +
           "COALESCE(SUM(oi.salePrice * oi.quantity), 0) as revenu " +
           "FROM MarketerProductSelection mps " +
           "JOIN mps.product p " +
           "LEFT JOIN OrderItem oi ON oi.product.id = p.id AND oi.order.marketer.id = :marketerId " +
           "WHERE mps.user.id = :marketerId " +
           "AND (mps.pendingDeletion = false OR mps.pendingDeletion IS NULL) " +
           "GROUP BY p.id, p.name, p.imageUrl, p.thumbnailUrl, p.price, p.promotionPrice, p.promotion " +
           "ORDER BY totalCommandes DESC, revenu DESC")
    List<Object[]> findTopProductsByMarketer(@Param("marketerId") Long marketerId, Pageable pageable);

    /**
     * Récupère la répartition des produits par gamme de prix pour un marketer
     * Alternative professionnelle aux catégories
     */
    @Query("SELECT " +
           "CASE " +
           "  WHEN p.price < 50 THEN 'Économique (< 50€)' " +
           "  WHEN p.price BETWEEN 50 AND 200 THEN 'Moyen (50€ - 200€)' " +
           "  ELSE 'Premium (> 200€)' " +
           "END as gamme_prix, " +
           "COUNT(mps) as totalProduits, " +
           "SUM(CASE WHEN p.promotion IS NOT NULL AND p.promotion > 0 THEN 1 ELSE 0 END) as produitsWowPrice " +
           "FROM MarketerProductSelection mps " +
           "JOIN mps.product p " +
           "WHERE mps.user.id = :marketerId " +
           "AND (mps.pendingDeletion = false OR mps.pendingDeletion IS NULL) " +
           "GROUP BY " +
           "CASE " +
           "  WHEN p.price < 50 THEN 'Économique (< 50€)' " +
           "  WHEN p.price BETWEEN 50 AND 200 THEN 'Moyen (50€ - 200€)' " +
           "  ELSE 'Premium (> 200€)' " +
           "END " +
           "ORDER BY MIN(p.price)")
    List<Object[]> findProductPriceDistributionByMarketer(@Param("marketerId") Long marketerId);

    /**
     * Ancienne méthode conservée pour compatibilité (peut être supprimée plus tard)
     */
    @Query("SELECT " +
           "CASE WHEN p.promotion IS NOT NULL AND p.promotion > 0 THEN 'Produits en promotion' ELSE 'Produits normaux' END as categorie, " +
           "COUNT(mps) as totalProduits, " +
           "SUM(CASE WHEN p.promotion IS NOT NULL AND p.promotion > 0 THEN 1 ELSE 0 END) as produitsWowPrice " +
           "FROM MarketerProductSelection mps " +
           "JOIN mps.product p " +
           "WHERE mps.user.id = :marketerId " +
           "AND (mps.pendingDeletion = false OR mps.pendingDeletion IS NULL) " +
           "GROUP BY CASE WHEN p.promotion IS NOT NULL AND p.promotion > 0 THEN 'Produits en promotion' ELSE 'Produits normaux' END")
    List<Object[]> findProductCategoryDistributionByMarketer(@Param("marketerId") Long marketerId);
}
