package com.forlivraison.web.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * DTO pour les données des graphiques du dashboard marketer
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Données des graphiques du dashboard marketer")
public class MarketerDashboardChartsDto {

    @Schema(description = "Évolution des commandes sur 30 jours")
    private List<CommandeEvolutionDto> evolutionCommandes;

    @Schema(description = "Répartition des produits par catégorie")
    private List<ProductCategoryDto> repartitionProduits;
}
