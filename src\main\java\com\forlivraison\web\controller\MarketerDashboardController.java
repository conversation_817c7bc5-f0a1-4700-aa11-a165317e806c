package com.forlivraison.web.controller;

import com.forlivraison.web.dto.*;
import com.forlivraison.web.service.MarketerDashboardService;
import com.forlivraison.web.security.jwt.JwtProvider;
import com.forlivraison.web.dao.UserRepository;
import com.forlivraison.web.dao.OrderRepository;
import com.forlivraison.web.dao.MarketerProductSelectionRepository;
import com.forlivraison.web.dao.CommissionRepository;
import com.forlivraison.web.entity.User;
import com.forlivraison.web.entity.Order;
import com.forlivraison.web.entity.MarketerProductSelection;
import com.forlivraison.web.enums.UserStatus;
import com.forlivraison.web.enums.OrderStatus;
import org.springframework.data.domain.PageRequest;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;

@RestController
@RequestMapping("/api/marketer/dashboard")
@RequiredArgsConstructor
@Slf4j
@Hidden
@CrossOrigin(origins = "http://localhost:4200")
public class MarketerDashboardController {

    private final MarketerDashboardService dashboardService;
    private final JwtProvider jwtProvider;
    private final UserRepository userRepository;
    private final OrderRepository orderRepository;
    private final MarketerProductSelectionRepository marketerProductSelectionRepository;
    private final CommissionRepository commissionRepository;

    /**
     * Récupère les statistiques principales pour le dashboard du marketer
     */
    @GetMapping("/stats")
    public ResponseEntity<MarketerDashboardStatsDto> getDashboardStats(HttpServletRequest request) {
        try {
            Long marketerId = extractMarketerIdFromToken(request);
            log.info("Récupération des statistiques dashboard pour le marketer ID: {}", marketerId);

            MarketerDashboardStatsDto stats = dashboardService.getDashboardStats(marketerId);
            log.info("Statistiques récupérées avec succès pour le marketer {}", marketerId);
            return ResponseEntity.ok(stats);

        } catch (IllegalArgumentException e) {
            log.warn("Erreur de validation pour les statistiques dashboard: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("Erreur lors de la récupération des statistiques dashboard pour le marketer", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Récupère les données pour les graphiques du dashboard
     */
    @GetMapping("/charts")
    public ResponseEntity<MarketerDashboardChartsDto> getDashboardCharts(HttpServletRequest request) {
        try {
            Long marketerId = extractMarketerIdFromToken(request);
            log.info("Récupération des données graphiques pour le marketer ID: {}", marketerId);
            
            MarketerDashboardChartsDto charts = dashboardService.getDashboardCharts(marketerId);
            return ResponseEntity.ok(charts);
            
        } catch (IllegalArgumentException e) {
            log.warn("Erreur de validation pour les graphiques dashboard: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("Erreur lors de la récupération des données graphiques", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Récupère les dernières commandes du marketer
     */
    @GetMapping("/recent-orders")
    public ResponseEntity<List<RecentOrderDto>> getRecentOrders(
            @RequestParam(defaultValue = "10") int limit,
            HttpServletRequest request) {
        try {
            Long marketerId = extractMarketerIdFromToken(request);
            log.info("Récupération des {} dernières commandes pour le marketer ID: {}", limit, marketerId);
            
            // Limiter à maximum 20 commandes
            if (limit > 20) {
                limit = 20;
            }
            
            List<RecentOrderDto> recentOrders = dashboardService.getRecentOrders(marketerId, limit);
            return ResponseEntity.ok(recentOrders);
            
        } catch (IllegalArgumentException e) {
            log.warn("Erreur de validation pour les commandes récentes: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("Erreur lors de la récupération des commandes récentes", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Récupère le top des produits les plus performants
     */
    @GetMapping("/top-products")
    public ResponseEntity<List<TopProductDto>> getTopProducts(
            @RequestParam(defaultValue = "5") int limit,
            HttpServletRequest request) {
        try {
            Long marketerId = extractMarketerIdFromToken(request);
            log.info("Récupération du top {} des produits pour le marketer ID: {}", limit, marketerId);
            
            // Limiter à maximum 10 produits
            if (limit > 10) {
                limit = 10;
            }
            
            List<TopProductDto> topProducts = dashboardService.getTopProducts(marketerId, limit);
            return ResponseEntity.ok(topProducts);
            
        } catch (IllegalArgumentException e) {
            log.warn("Erreur de validation pour le top produits: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("Erreur lors de la récupération du top produits", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Récupère les informations du marketer connecté
     */
    @GetMapping("/profile")
    public ResponseEntity<Map<String, Object>> getMarketerProfile(HttpServletRequest request) {
        try {
            Long marketerId = extractMarketerIdFromToken(request);
            log.info("Récupération du profil pour le marketer ID: {}", marketerId);

            // Récupérer l'utilisateur depuis la base de données
            User marketer = userRepository.findById(marketerId).orElse(null);
            if (marketer == null) {
                log.warn("Marketer non trouvé avec l'ID: {}", marketerId);
                return ResponseEntity.notFound().build();
            }

            // Créer la réponse avec les informations nécessaires
            Map<String, Object> profile = new HashMap<>();
            profile.put("id", marketer.getId());
            profile.put("firstName", marketer.getFirstName());
            profile.put("lastName", marketer.getLastName());
            profile.put("email", marketer.getEmail());
            profile.put("telephone", marketer.getTelephone());
            profile.put("subscriptionType", marketer.getSubscriptionType());

            return ResponseEntity.ok(profile);

        } catch (IllegalArgumentException e) {
            log.warn("Erreur de validation pour le profil marketer: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("Erreur lors de la récupération du profil marketer", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Endpoint de test pour vérifier que le contrôleur fonctionne
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "Dashboard Marketer API");
        health.put("timestamp", LocalDateTime.now().toString());
        health.put("version", "1.0.0");
        return ResponseEntity.ok(health);
    }

    /**
     * Endpoint de test sans authentification pour déboguer les commandes
     */
    @GetMapping("/debug-orders/{marketerId}")
    public ResponseEntity<Map<String, Object>> debugOrders(@PathVariable Long marketerId) {
        Map<String, Object> debug = new HashMap<>();
        debug.put("marketerId", marketerId);
        debug.put("timestamp", LocalDateTime.now().toString());

        try {
            // Test de toutes les commandes du marketer
            List<Order> allOrders = orderRepository.findRecentOrdersByMarketer(
                marketerId,
                PageRequest.of(0, 20)
            );
            debug.put("totalOrdersInDB", allOrders.size());

            // Détails des commandes
            List<Map<String, Object>> orderDetails = new ArrayList<>();
            for (Order order : allOrders) {
                Map<String, Object> orderInfo = new HashMap<>();
                orderInfo.put("id", order.getId());
                orderInfo.put("status", order.getStatus());
                orderInfo.put("orderDate", order.getOrderDate());
                orderInfo.put("totalAmount", order.getTotalAmount());
                orderInfo.put("marketer", order.getMarketer() != null ? order.getMarketer().getId() : null);
                orderDetails.add(orderInfo);
            }
            debug.put("orderDetails", orderDetails);

            // Comptages par statut
            Long pendingOrders = orderRepository.countByMarketerIdAndStatus(marketerId, OrderStatus.PENDING);
            Long confirmedOrders = orderRepository.countByMarketerIdAndStatus(marketerId, OrderStatus.CONFIRMED);
            Long deliveredOrders = orderRepository.countByMarketerIdAndStatus(marketerId, OrderStatus.DELIVERED);

            debug.put("pendingOrders", pendingOrders);
            debug.put("confirmedOrders", confirmedOrders);
            debug.put("deliveredOrders", deliveredOrders);

            // Test avec plage de dates du mois actuel
            LocalDate debutMoisActuel = LocalDate.now().withDayOfMonth(1);
            LocalDate finMoisActuel = LocalDate.now();
            LocalDateTime startDateTime = debutMoisActuel.atStartOfDay();
            LocalDateTime endDateTime = finMoisActuel.atTime(23, 59, 59);

            Long confirmedThisMonth = orderRepository.countByMarketerIdAndStatusAndDateRange(
                marketerId, OrderStatus.CONFIRMED, startDateTime, endDateTime);
            Long deliveredThisMonth = orderRepository.countByMarketerIdAndStatusAndDateRange(
                marketerId, OrderStatus.DELIVERED, startDateTime, endDateTime);

            debug.put("confirmedThisMonth", confirmedThisMonth);
            debug.put("deliveredThisMonth", deliveredThisMonth);
            debug.put("dateRange", "Du " + startDateTime + " au " + endDateTime);

            return ResponseEntity.ok(debug);

        } catch (Exception e) {
            debug.put("error", e.getMessage());
            debug.put("stackTrace", e.getStackTrace());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(debug);
        }
    }

    /**
     * Endpoint de diagnostic complet
     */
    @GetMapping("/diagnostic")
    public ResponseEntity<Map<String, Object>> diagnostic(HttpServletRequest request) {
        Map<String, Object> diagnostic = new HashMap<>();
        diagnostic.put("timestamp", LocalDateTime.now().toString());

        try {
            // Test d'authentification
            Long marketerId = extractMarketerIdFromToken(request);
            diagnostic.put("authentication", "SUCCESS");
            diagnostic.put("marketerId", marketerId);

            // Test des repositories
            Map<String, Object> repositoryTests = new HashMap<>();

            // Test OrderRepository
            try {
                Long pendingOrders = orderRepository.countByMarketerIdAndStatus(marketerId, OrderStatus.PENDING);
                repositoryTests.put("orderRepository_countPending", pendingOrders);
            } catch (Exception e) {
                repositoryTests.put("orderRepository_countPending_error", e.getMessage());
            }

            // Test MarketerProductSelectionRepository
            try {
                Long activeProducts = marketerProductSelectionRepository.countActiveProductsByMarketer(marketerId);
                repositoryTests.put("productSelectionRepository_countActive", activeProducts);
            } catch (Exception e) {
                repositoryTests.put("productSelectionRepository_countActive_error", e.getMessage());
            }

            // Test CommissionRepository
            try {
                BigDecimal unpaidCommissions = commissionRepository.sumUnpaidCommissionsByMarketer(
                    marketerId, com.forlivraison.web.enums.CommissionStatus.UNPAID);
                repositoryTests.put("commissionRepository_sumUnpaid", unpaidCommissions);
            } catch (Exception e) {
                repositoryTests.put("commissionRepository_sumUnpaid_error", e.getMessage());
            }

            diagnostic.put("repositoryTests", repositoryTests);
            diagnostic.put("status", "SUCCESS");

        } catch (Exception e) {
            diagnostic.put("authentication", "FAILED");
            diagnostic.put("error", e.getMessage());
            diagnostic.put("status", "ERROR");
        }

        return ResponseEntity.ok(diagnostic);
    }

    /**
     * Endpoint de test simple pour les statistiques
     */
    @GetMapping("/test-stats")
    public ResponseEntity<Map<String, Object>> testStats(HttpServletRequest request) {
        try {
            Long marketerId = extractMarketerIdFromToken(request);
            log.info("Test des statistiques pour le marketer ID: {}", marketerId);

            Map<String, Object> testData = new HashMap<>();
            testData.put("marketerId", marketerId);
            testData.put("timestamp", LocalDateTime.now().toString());
            testData.put("status", "OK");

            // Test des repositories
            try {
                Long totalProduits = dashboardService.getTotalProduitsActifs(marketerId);
                testData.put("totalProduitsActifs", totalProduits);

                Long totalProduitsWow = dashboardService.getTotalProduitsWowPrice(marketerId);
                testData.put("totalProduitsWowPrice", totalProduitsWow);

                // Test détaillé des produits sélectionnés
                List<MarketerProductSelection> selections = marketerProductSelectionRepository.findByUserIdWithProduct(marketerId);
                List<Map<String, Object>> produitsDetails = new ArrayList<>();
                for (MarketerProductSelection selection : selections) {
                    Map<String, Object> produitInfo = new HashMap<>();
                    produitInfo.put("id", selection.getProduct().getId());
                    produitInfo.put("name", selection.getProduct().getName());
                    produitInfo.put("price", selection.getProduct().getPrice());
                    produitInfo.put("wowPrice", selection.getProduct().isWowPrice());
                    produitInfo.put("promotion", selection.getProduct().getPromotion());
                    produitInfo.put("pendingDeletion", selection.getPendingDeletion());
                    produitsDetails.add(produitInfo);
                }
                testData.put("produitsSelectionnes", produitsDetails);

            } catch (Exception e) {
                testData.put("errorTotalProduits", e.getMessage());
            }

            try {
                Long commandesEnAttente = dashboardService.getCommandesEnAttente(marketerId);
                testData.put("commandesEnAttente", commandesEnAttente);

                // Test de toutes les commandes du marketer (sans filtre de date)
                List<Order> allOrders = orderRepository.findRecentOrdersByMarketer(
                    marketerId,
                    PageRequest.of(0, 10)
                );
                testData.put("totalOrdersInDB", allOrders.size());

                // Détails des commandes trouvées
                List<Map<String, Object>> orderDetails = new ArrayList<>();
                for (Order order : allOrders) {
                    Map<String, Object> orderInfo = new HashMap<>();
                    orderInfo.put("id", order.getId());
                    orderInfo.put("status", order.getStatus());
                    orderInfo.put("orderDate", order.getOrderDate());
                    orderInfo.put("totalAmount", order.getTotalAmount());
                    orderInfo.put("marketer", order.getMarketer() != null ? order.getMarketer().getId() : null);
                    orderDetails.add(orderInfo);
                }
                testData.put("orderDetails", orderDetails);

                // Test spécifique pour les commandes confirmées
                Long confirmedOrders = orderRepository.countByMarketerIdAndStatus(marketerId, OrderStatus.CONFIRMED);
                testData.put("totalConfirmedOrders", confirmedOrders);

                Long deliveredOrders = orderRepository.countByMarketerIdAndStatus(marketerId, OrderStatus.DELIVERED);
                testData.put("totalDeliveredOrders", deliveredOrders);

                // Test avec plage de dates du mois actuel
                LocalDate debutMoisActuel = LocalDate.now().withDayOfMonth(1);
                LocalDate finMoisActuel = LocalDate.now();
                LocalDateTime startDateTime = debutMoisActuel.atStartOfDay();
                LocalDateTime endDateTime = finMoisActuel.atTime(23, 59, 59);

                Long confirmedThisMonth = orderRepository.countByMarketerIdAndStatusAndDateRange(
                    marketerId, OrderStatus.CONFIRMED, startDateTime, endDateTime);
                testData.put("confirmedOrdersThisMonth", confirmedThisMonth);

                Long deliveredThisMonth = orderRepository.countByMarketerIdAndStatusAndDateRange(
                    marketerId, OrderStatus.DELIVERED, startDateTime, endDateTime);
                testData.put("deliveredOrdersThisMonth", deliveredThisMonth);

                testData.put("dateRange", "Du " + startDateTime + " au " + endDateTime);

            } catch (Exception e) {
                testData.put("errorCommandesEnAttente", e.getMessage());
            }

            return ResponseEntity.ok(testData);

        } catch (Exception e) {
            log.error("Erreur lors du test des statistiques", e);
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("error", e.getMessage());
            errorData.put("timestamp", LocalDateTime.now().toString());
            errorData.put("stackTrace", e.getStackTrace());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorData);
        }
    }

    /**
     * Version simplifiée des statistiques pour diagnostic
     */
    @GetMapping("/simple-stats")
    public ResponseEntity<MarketerDashboardStatsDto> getSimpleStats(HttpServletRequest request) {
        try {
            Long marketerId = extractMarketerIdFromToken(request);
            log.info("Récupération des statistiques simples pour le marketer ID: {}", marketerId);

            // Test avec les vraies méthodes du service
            MarketerDashboardStatsDto stats = dashboardService.getDashboardStats(marketerId);

            log.info("Statistiques récupérées avec succès pour le marketer {}", marketerId);
            return ResponseEntity.ok(stats);

        } catch (Exception e) {
            log.error("Erreur lors de la récupération des statistiques simples pour le marketer", e);

            // En cas d'erreur, retourner des valeurs par défaut
            MarketerDashboardStatsDto stats = new MarketerDashboardStatsDto();
            stats.setTotalProduitsActifs(0L);
            stats.setTotalProduitsWowPrice(0L);
            stats.setVariationProduitsVsPrecedent(0.0);

            stats.setCommandesConfirmeesLivrees(0L);
            stats.setCommandesConfirmeesEnCours(0L);
            stats.setTotalCommandesConfirmees(0L);
            stats.setVariationCommandesConfirmeesVsPrecedent(0.0);

            stats.setTauxConversion(0.0);
            stats.setVariationTauxConversionVsPrecedent(0.0);

            stats.setChiffreAffairesMois(java.math.BigDecimal.ZERO);
            stats.setCommissionsEnAttente(java.math.BigDecimal.ZERO);
            stats.setDernierPaiement(java.math.BigDecimal.ZERO);

            stats.setPeriodeActuelle("Période actuelle");
            stats.setPeriodePrecedente("Période précédente");

            log.info("Statistiques par défaut retournées suite à une erreur");
            return ResponseEntity.ok(stats);
        }
    }

    /**
     * Extrait l'ID du marketer depuis le token JWT
     */
    private Long extractMarketerIdFromToken(HttpServletRequest request) {
        try {
            String email = null;

            // Méthode 1: Essayer d'extraire le token du header Authorization
            String authHeader = request.getHeader("Authorization");
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                String token = authHeader.substring(7);
                email = jwtProvider.getUserNameFromJwtToken(token);
                log.debug("Email extrait du token JWT: {}", email);
            }

            // Méthode 2: Utiliser le SecurityContext si le token n'est pas dans le header
            if (email == null) {
                Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                if (authentication != null && authentication.getName() != null && !authentication.getName().equals("anonymousUser")) {
                    email = authentication.getName();
                    log.debug("Email extrait du SecurityContext: {}", email);
                }
            }

            if (email == null || email.trim().isEmpty()) {
                log.error("Aucun email trouvé dans le token JWT ou le SecurityContext");
                throw new RuntimeException("Token JWT invalide ou manquant");
            }

            // Rechercher l'utilisateur
            User user = userRepository.findOneByEmail2(email, UserStatus.ACTIVE);
            if (user == null) {
                log.error("Utilisateur non trouvé ou inactif avec l'email: {}", email);
                throw new RuntimeException("Utilisateur non trouvé ou inactif: " + email);
            }

            // Vérifier que c'est bien un marketer
            if (user.getRole() == null || !user.getRole().getName().equals(com.forlivraison.web.enums.RoleName.MARKETER)) {
                log.error("L'utilisateur {} n'est pas un marketer. Rôle: {}", email,
                    user.getRole() != null ? user.getRole().getName() : "null");
                throw new RuntimeException("Accès non autorisé: utilisateur n'est pas un marketer");
            }

            log.info("Marketer authentifié avec succès - ID: {}, Email: {}", user.getId(), email);
            return user.getId();

        } catch (Exception e) {
            log.error("Erreur lors de l'extraction de l'ID du marketer depuis le token: {}", e.getMessage(), e);
            throw new RuntimeException("Erreur d'authentification: " + e.getMessage(), e);
        }
    }
}
