package com.forlivraison.web.dao;

import com.forlivraison.web.entity.Commission;
import com.forlivraison.web.entity.Order;
import com.forlivraison.web.entity.User;
import com.forlivraison.web.enums.CommissionStatus;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Pageable;

@Repository
public interface CommissionRepository extends GenericRepository<Commission, Long> {


    // ===== MÉTHODES POUR LA GÉNÉRATION DE FACTURES =====

    @Query("SELECT c FROM Commission c WHERE c.marketer = :marketer " +
           "AND c.invoiceDate IS NULL " +
           "AND c.order.status = 'DELIVERED' " +
           "AND c.order.facture IS NULL")
    List<Commission> findUnbilledCommissionsByMarketer(@Param("marketer") User marketer);

    @Query("SELECT c FROM Commission c WHERE c.marketer.id = :marketerId " +
           "AND c.invoiceDate IS NULL " +
           "AND c.order.status = 'DELIVERED' " +
           "AND c.order.facture IS NULL")
    List<Commission> findUnbilledCommissionsByMarketerId(@Param("marketerId") Long marketerId);

    @Query("SELECT SUM(c.amount) FROM Commission c WHERE c.marketer.id = :marketerId " +
           "AND c.invoiceDate IS NULL " +
           "AND c.order.status = 'DELIVERED' " +
           "AND c.order.facture IS NULL")
    BigDecimal sumUnbilledCommissionsByMarketerId(@Param("marketerId") Long marketerId);

    // ===== MÉTHODES POUR VÉRIFICATION =====

    @Query("SELECT COUNT(c) FROM Commission c WHERE c.order = :order")
    long countCommissionsByOrder(@Param("order") Order order);

    boolean existsByOrder(Order order);

    // ===== MÉTHODES POUR LES STATISTIQUES =====

    @Query("SELECT COUNT(c) FROM Commission c WHERE c.marketer.id = :marketerId")
    long countCommissionsByMarketerId(@Param("marketerId") Long marketerId);

    @Query("SELECT SUM(c.amount) FROM Commission c WHERE c.marketer.id = :marketerId")
    BigDecimal sumCommissionsByMarketerId(@Param("marketerId") Long marketerId);

    @Query("SELECT COUNT(c) FROM Commission c WHERE c.status = :status")
    long countCommissionsByStatus(@Param("status") CommissionStatus status);

    // ===== NOUVELLES MÉTHODES POUR LE DASHBOARD MARKETER =====

    /**
     * Somme des commissions non payées pour un marketer
     */
    @Query("SELECT COALESCE(SUM(c.amount), 0) FROM Commission c " +
           "WHERE c.marketer.id = :marketerId AND c.status = :status")
    BigDecimal sumUnpaidCommissionsByMarketer(@Param("marketerId") Long marketerId,
                                             @Param("status") CommissionStatus status);

    /**
     * Somme des commissions payées pour un marketer
     */
    @Query("SELECT COALESCE(SUM(c.amount), 0) FROM Commission c " +
           "WHERE c.marketer.id = :marketerId AND c.status = 'PAID'")
    BigDecimal sumPaidCommissionsByMarketer(@Param("marketerId") Long marketerId);

    /**
     * Dernière commission payée pour un marketer
     */
    @Query("SELECT c.amount FROM Commission c " +
           "WHERE c.marketer.id = :marketerId AND c.status = 'PAID' " +
           "ORDER BY c.invoiceDate DESC")
    List<BigDecimal> findLastPaidCommissionByMarketer(@Param("marketerId") Long marketerId, Pageable pageable);

    /**
     * Commissions par plage de dates
     */
    @Query("SELECT COALESCE(SUM(c.amount), 0) FROM Commission c " +
           "WHERE c.marketer.id = :marketerId " +
           "AND c.order.orderDate BETWEEN :startDate AND :endDate")
    BigDecimal sumCommissionsByMarketerAndDateRange(@Param("marketerId") Long marketerId,
                                                   @Param("startDate") Date startDate,
                                                   @Param("endDate") Date endDate);

    /**
     * Nombre de commissions par plage de dates
     */
    @Query("SELECT COUNT(c) FROM Commission c " +
           "WHERE c.marketer.id = :marketerId " +
           "AND c.order.orderDate BETWEEN :startDate AND :endDate")
    Long countCommissionsByMarketerAndDateRange(@Param("marketerId") Long marketerId,
                                               @Param("startDate") Date startDate,
                                               @Param("endDate") Date endDate);

    // ===== MÉTHODES POUR LES FACTURES =====

    @Query("SELECT c FROM Commission c " +
           "LEFT JOIN FETCH c.order o " +
           "LEFT JOIN FETCH o.orderItems oi " +
           "LEFT JOIN FETCH oi.product " +
           "WHERE c.order.facture.id = :factureId")
    List<Commission> findCommissionsByFactureId(@Param("factureId") Long factureId);

    /**
     * Trouve les commissions liées à une facture
     */
    @Query("SELECT c FROM Commission c WHERE c.order.facture.id = :invoiceId")
    List<Commission> findByInvoiceId(@Param("invoiceId") Long invoiceId);


    List<Commission> findByMarketerIdAndDateBetween(Long marketerId, Date startDate, Date endDate);

    @Query("SELECT c FROM Commission c WHERE c.marketer.id = :marketerId AND c.status = 'PAID' ORDER BY c.invoiceDate DESC")
    Optional<Commission> findLastPaidCommissionByMarketerId(@Param("marketerId") Long marketerId);

}
