import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ChangeDetectorRef } from "@angular/core"
import { Subject } from "rxjs"
import { takeUntil, finalize, catchError } from "rxjs/operators"
import { MatSnackBar } from "@angular/material/snack-bar"
import { of } from "rxjs"

// ApexCharts
import type {
  ApexAxisChartSeries,
  ApexChart,
  ApexXAxis,
  ApexDataLabels,
  ApexTooltip,
  ApexStroke,
  ApexPlotOptions,
  ApexYAxis,
  ApexFill,
  ApexLegend,
  ApexResponsive,
  ApexNonAxisChartSeries,
  ChartComponent,
} from "ng-apexcharts"

import { DashboardMarketerService } from "./dashboard-marketer.service"
import { TokenStorageService } from "../login/authentication/token-storage.service"
import {
  MarketerDashboardStats,
  MarketerDashboardCharts,
  RecentOrder,
  TopProduct,
  KpiCard,
} from "./dashboard-marketer.model"

// Types pour les graphiques ApexCharts
export type ChartOptions = {
  series: ApexAxisChartSeries
  chart: ApexChart
  xaxis: ApexXAxis
  dataLabels: ApexDataLabels
  tooltip: ApexTooltip
  stroke: ApexStroke
  plotOptions: ApexPlotOptions
  yaxis: ApexYAxis
  fill: ApexFill
  legend: ApexLegend
  responsive: ApexResponsive[]
  colors: string[]
}

export type DonutChartOptions = {
  series: ApexNonAxisChartSeries
  chart: ApexChart
  labels: string[]
  dataLabels: ApexDataLabels
  tooltip: ApexTooltip
  legend: ApexLegend
  responsive: ApexResponsive[]
  colors: string[]
  plotOptions: ApexPlotOptions
}

@Component({
  selector: "app-dashboard-marketer",
  templateUrl: "./dashboard-marketer.component.html",
  styleUrls: ["./dashboard-marketer.component.scss"],
})
export class DashboardMarketerComponent implements OnInit, OnDestroy {
  // Loading states
  isLoadingStats = true
  isLoadingCharts = true
  isLoadingRecentOrders = true
  isLoadingTopProducts = true
  isRefreshing = false

  // Data
  stats: MarketerDashboardStats | null = null
  charts: MarketerDashboardCharts | null = null
  recentOrders: RecentOrder[] = []
  topProducts: TopProduct[] = []

  // KPI Cards configuration
  kpiCards: KpiCard[] = []

  // ApexCharts ViewChild
  @ViewChild("lineChart") lineChart!: ChartComponent
  @ViewChild("donutChart") donutChart!: ChartComponent
  @ViewChild("barChart") barChart!: ChartComponent

  // Chart options modernes
  public lineChartOptions: Partial<ChartOptions> = {}
  public donutChartOptions: Partial<DonutChartOptions> = {}
  public barChartOptions: Partial<ChartOptions> = {}

  // Header data
  marketerName = ""
  marketerFullName = ""
  selectedPeriodLabel = "les 30 derniers jours"

  // Exposer Math pour le template
  Math = Math

  private destroy$ = new Subject<void>()

  constructor(
    private dashboardService: DashboardMarketerService,
    private snackBar: MatSnackBar,
    private tokenStorage: TokenStorageService,
    private cdr: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    console.log("🚀 Initialisation du dashboard Affilink")
    this.initializeMarketerInfo()
    this.loadDashboardData()
  }

  ngOnDestroy(): void {
    this.destroy$.next()
    this.destroy$.complete()
  }

  /**
   * Initialise les informations du marketer
   */
  private initializeMarketerInfo(): void {
    // Récupérer les informations complètes du marketer
    this.dashboardService
      .getMarketerInfo()
      .pipe(
        takeUntil(this.destroy$),
        catchError((error) => {
          console.error("❌ Erreur lors du chargement des informations marketer:", error)
          // Fallback vers le username du token
          const username = this.tokenStorage.getUsername()
          this.marketerName = username || "Marketer"
          this.marketerFullName = this.marketerName
          return of(null)
        }),
      )
      .subscribe({
        next: (marketerInfo) => {
          if (marketerInfo) {
            this.marketerFullName = `${marketerInfo.firstName} ${marketerInfo.lastName}`
            this.marketerName = this.marketerFullName
            console.log("👤 Informations marketer chargées:", marketerInfo)
          }
        },
      })
  }

  /**
   * Charge toutes les données du dashboard
   */
  loadDashboardData(): void {
    console.log("📊 Chargement des données du dashboard...")
    this.loadStats()
    this.loadCharts()
    this.loadRecentOrders()
    this.loadTopProducts()
  }

  /**
   * Charge les statistiques principales avec gestion d'erreur améliorée
   */
  loadStats(): void {
    this.isLoadingStats = true

    this.dashboardService
      .getDashboardStats()
      .pipe(
        takeUntil(this.destroy$),
        catchError((error) => {
          console.error("❌ Erreur lors du chargement des statistiques:", error)
          this.showError("Erreur lors du chargement des statistiques")
          // Retourner des données par défaut pour éviter les erreurs
          return of(this.getDefaultStats())
        }),
        finalize(() => {
          this.isLoadingStats = false
          this.cdr.detectChanges()
        }),
      )
      .subscribe({
        next: (stats) => {
          this.stats = stats
          console.log("📊 Statistiques chargées:", stats)
          this.setupKpiCards()
          this.cdr.detectChanges()
        },
      })
  }

  /**
   * Charge les données des graphiques avec gestion d'erreur
   */
  loadCharts(): void {
    this.isLoadingCharts = true

    this.dashboardService
      .getDashboardCharts()
      .pipe(
        takeUntil(this.destroy$),
        catchError((error) => {
          console.error("❌ Erreur lors du chargement des graphiques:", error)
          this.showError("Erreur lors du chargement des graphiques")
          return of(this.getDefaultCharts())
        }),
        finalize(() => {
          this.isLoadingCharts = false
          this.cdr.detectChanges()
        }),
      )
      .subscribe({
        next: (charts) => {
          this.charts = charts
          this.setupChartOptions()
          console.log("📈 Données graphiques chargées:", charts)
          this.cdr.detectChanges()
        },
      })
  }

  /**
   * Charge les commandes récentes - VRAIES DONNÉES
   */
  loadRecentOrders(): void {
    this.isLoadingRecentOrders = true

    this.dashboardService
      .getRecentOrders(10)
      .pipe(
        takeUntil(this.destroy$),
        catchError((error) => {
          console.error("❌ Erreur lors du chargement des commandes récentes:", error)
          // En cas d'erreur, ne pas afficher de données factices
          this.showError("Impossible de charger les commandes récentes")
          return of([])
        }),
        finalize(() => {
          this.isLoadingRecentOrders = false
          this.cdr.detectChanges()
        }),
      )
      .subscribe({
        next: (orders) => {
          this.recentOrders = orders || []
          console.log("📦 Commandes récentes chargées:", orders?.length || 0, "commandes")
          console.log("📦 Données des commandes:", orders)
        },
      })
  }

  /**
   * Charge le top des produits - VRAIES DONNÉES
   */
  loadTopProducts(): void {
    this.isLoadingTopProducts = true

    this.dashboardService
      .getTopProducts(5)
      .pipe(
        takeUntil(this.destroy$),
        catchError((error) => {
          console.error("❌ Erreur lors du chargement du top produits:", error)
          // En cas d'erreur, ne pas afficher de données factices
          this.showError("Impossible de charger le top des produits")
          return of([])
        }),
        finalize(() => {
          this.isLoadingTopProducts = false
          this.isLoadingTopProducts = false
          this.cdr.detectChanges()
        }),
      )
      .subscribe({
        next: (products) => {
          this.topProducts = products || []
          console.log("🏆 Top produits chargés:", products?.length || 0, "produits")
          console.log("🏆 Données des produits:", products)
          // Mettre à jour le graphique en barres avec les vraies données
          this.setupBarChart()
        },
      })
  }

  /**
   * Configure les 6 cartes KPI optimisées selon les spécifications
   */
  setupKpiCards(): void {
    if (!this.stats) return

    this.kpiCards = [
      {
        title: "Produits promus actifs",
        value: this.stats.totalProduitsActifs || 0,
        variation: this.stats.variationProduitsVsPrecedent,
        variationLabel: "vs mois précédent",
        icon: "local_fire_department",
        color: "primary",
        format: "number",
        showProgress: false,
      },
      {
        title: "Commandes confirmées",
        value: this.stats.totalCommandesConfirmees || 0,
        variation: this.stats.variationCommandesConfirmeesVsPrecedent,
        variationLabel: "vs mois précédent",
        icon: "check_circle",
        color: "success",
        format: "number",
        showProgress: false,
      },
      {
        title: "Commandes en attente",
        value: this.stats.commandesEnAttente || 0,
        variation: this.stats.variationCommandesEnAttenteVsPrecedent,
        variationLabel: "vs mois précédent",
        icon: "pending",
        color: "warning",
        format: "number",
        showProgress: false,
      },
      {
        title: "Commissions totales générées",
        value: this.stats.totalCommissionsGenerees || 0,
        variation: undefined,
        variationLabel: "ce mois",
        icon: "account_balance_wallet",
        color: "accent",
        format: "currency",
        showProgress: false,
      },
      {
        title: "Taux de conversion",
        value: this.stats.tauxConversion || 0,
        variation: this.stats.variationTauxConversionVsPrecedent,
        variationLabel: "vs mois précédent",
        icon: "trending_up",
        color: "info",
        format: "percentage",
        showProgress: true,
      },
      {
        title: "Factures disponibles",
        value: this.stats.facturesDisponibles || 0,
        variation: undefined,
        variationLabel: `${this.stats.facturesPayees || 0} payées`,
        icon: "receipt",
        color: "secondary",
        format: "number",
        showProgress: false,
      },
    ]

    console.log("🎯 KPI Cards configurées:", this.kpiCards)
  }

  /**
   * Configure les options des graphiques ApexCharts modernes
   */
  setupChartOptions(): void {
    this.setupLineChart()
    this.setupDonutChart()
    this.setupBarChart()
  }

  /**
   * Configuration du graphique d'évolution des commandes (Line Chart) - CORRIGÉ
   */
  private setupLineChart(): void {
    const evolutionData = this.charts?.evolutionCommandes || []
    console.log("📈 Configuration graphique évolution avec données:", evolutionData)

    // Générer les 30 derniers jours
    const dates: string[] = []
    const confirmedData: number[] = []
    const rejectedData: number[] = []

    for (let i = 29; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      const dateStr = date.toISOString().split("T")[0] // Format YYYY-MM-DD
      dates.push(date.toLocaleDateString("fr-FR", { day: "2-digit", month: "2-digit" }))

      // Trouver les données pour cette date
      const dayData = evolutionData.find((d) => {
        const dataDate = new Date(d.date).toISOString().split("T")[0]
        return dataDate === dateStr
      })

      confirmedData.push(dayData?.commandesConfirmees || 0)
      rejectedData.push(dayData?.commandesRejetees || 0)
    }

    // Si pas de données réelles, générer des données de démonstration
    if (evolutionData.length === 0) {
      console.log("⚠️ Pas de données réelles, génération de données de démo")
      for (let i = 0; i < 30; i++) {
        confirmedData[i] = Math.floor(Math.random() * 8) + 1
        rejectedData[i] = Math.floor(Math.random() * 3)
      }
    }

    this.lineChartOptions = {
      series: [
        {
          name: "Confirmées",
          data: confirmedData,
          color: "#10b981",
        },
        {
          name: "Rejetées",
          data: rejectedData,
          color: "#ef4444",
        },
      ],
      chart: {
        height: 320,
        type: "line",
        toolbar: {
          show: true,
          tools: {
            download: true,
            selection: false,
            zoom: true,
            zoomin: true,
            zoomout: true,
            pan: false,
            reset: true,
          },
        },
        animations: {
          enabled: true,
          easing: "easeinout",
          speed: 800,
        },
      },
      colors: ["#10b981", "#ef4444"],
      dataLabels: {
        enabled: false,
      },
      stroke: {
        curve: "smooth",
        width: 3,
        lineCap: "round",
      },
      xaxis: {
        categories: dates,
        title: {
          text: "Période (30 derniers jours)",
          style: {
            fontSize: "12px",
            fontWeight: 600,
            color: "#374151",
          },
        },
        labels: {
          style: {
            fontSize: "11px",
            colors: "#6b7280",
          },
        },
      },
      yaxis: {
        title: {
          text: "Nombre de commandes",
          style: {
            fontSize: "12px",
            fontWeight: 600,
            color: "#374151",
          },
        },
        min: 0,
        labels: {
          style: {
            fontSize: "11px",
            colors: "#6b7280",
          },
        },
      },
      tooltip: {
        shared: true,
        intersect: false,
        y: {
          formatter: (val: number) => val + " commandes",
        },
      },
      legend: {
        position: "top",
        horizontalAlign: "center",
        fontSize: "14px",
        fontWeight: 600,
        markers: {
          width: 12,
          height: 12,
          radius: 6,
        },
      },
      fill: {
        type: "gradient",
        gradient: {
          shade: "light",
          type: "vertical",
          shadeIntensity: 0.3,
          gradientToColors: ["#34d399", "#f87171"],
          inverseColors: false,
          opacityFrom: 0.8,
          opacityTo: 0.1,
        },
      },
      responsive: [
        {
          breakpoint: 768,
          options: {
            chart: {
              height: 280,
            },
            legend: {
              position: "bottom",
            },
          },
        },
      ],
    }
  }

  /**
   * Configuration du graphique donut pour la répartition WOW Price - CORRIGÉ
   */
  private setupDonutChart(): void {
    if (!this.stats) return

    const totalProduits = this.stats.totalProduitsActifs || 0
    const produitsWowPrice = this.stats.totalProduitsWowPrice || 0
    const produitsNormaux = totalProduits - produitsWowPrice

    const labels: string[] = []
    const series: number[] = []
    const colors: string[] = []

    if (totalProduits > 0) {
      if (produitsWowPrice > 0) {
        labels.push("WOW Price")
        series.push(produitsWowPrice)
        colors.push("#664DC9")
      }

      if (produitsNormaux > 0) {
        labels.push("Prix normal")
        series.push(produitsNormaux)
        colors.push("#10b981")
      }
    } else {
      // Données par défaut si aucun produit
      labels.push("Aucun produit")
      series.push(1)
      colors.push("#e5e7eb")
    }

    this.donutChartOptions = {
      series: series,
      chart: {
        type: "donut",
        height: 200,
        animations: {
          enabled: true,
          easing: "easeinout",
          speed: 800,
        },
      },
      labels: labels,
      colors: colors,
      dataLabels: {
        enabled: true,
        formatter: (val: number) => Math.round(val) + "%",
        style: {
          fontSize: "14px",
          fontWeight: "bold",
          colors: ["#fff"],
        },
        dropShadow: {
          enabled: true,
        },
      },
      plotOptions: {
        pie: {
          donut: {
            size: "65%",
            labels: {
              show: true,
              total: {
                show: true,
                label: "Total",
                fontSize: "14px",
                fontWeight: 600,
                color: "#374151",
                formatter: (w: any) => totalProduits.toString(),
              },
              value: {
                show: true,
                fontSize: "20px",
                fontWeight: 700,
                color: "#664DC9",
              },
            },
          },
        },
      },
      legend: {
        position: "bottom",
        fontSize: "12px",
        fontWeight: 500,
        markers: {
          width: 10,
          height: 10,
          radius: 5,
        },
      },
      tooltip: {
        y: {
          formatter: (val: number) => val + " produits",
        },
      },
      responsive: [
        {
          breakpoint: 768,
          options: {
            chart: {
              height: 250,
            },
          },
        },
      ],
    }
  }

  /**
   * Configuration du graphique en barres pour le top produits - CORRIGÉ
   */
  private setupBarChart(): void {
    const topProducts = this.topProducts || []

    const categories: string[] = []
    const data: number[] = []

    if (topProducts.length > 0) {
      topProducts.slice(0, 5).forEach((product) => {
        categories.push(
          product.productName.length > 15 ? product.productName.substring(0, 15) + "..." : product.productName,
        )
        data.push(product.totalCommandes || 0)
      })
    } else {
      // Données par défaut si aucun produit
      categories.push("Aucun produit")
      data.push(0)
    }

    this.barChartOptions = {
      series: [
        {
          name: "Commandes",
          data: data,
          color: "#664DC9",
        },
      ],
      chart: {
        type: "bar",
        height: 200,
        toolbar: {
          show: false,
        },
        animations: {
          enabled: true,
          easing: "easeinout",
          speed: 800,
        },
      },
      colors: ["#664DC9"],
      plotOptions: {
        bar: {
          horizontal: true,
          borderRadius: 6,
          dataLabels: {
            position: "top",
          },
        },
      },
      dataLabels: {
        enabled: true,
        offsetX: 10,
        style: {
          fontSize: "11px",
          fontWeight: 600,
          colors: ["#fff"],
        },
      },
      xaxis: {
        categories: categories,
        title: {
          text: "Nombre de commandes",
          style: {
            fontSize: "11px",
            fontWeight: 600,
            color: "#374151",
          },
        },
        labels: {
          style: {
            fontSize: "10px",
            colors: "#6b7280",
          },
        },
      },
      yaxis: {
        title: {
          text: "Produits",
          style: {
            fontSize: "11px",
            fontWeight: 600,
            color: "#374151",
          },
        },
        labels: {
          style: {
            fontSize: "10px",
            colors: "#6b7280",
          },
        },
      },
      tooltip: {
        y: {
          formatter: (val: number) => val + " commandes",
        },
      },
      fill: {
        type: "gradient",
        gradient: {
          shade: "light",
          type: "horizontal",
          shadeIntensity: 0.4,
          gradientToColors: ["#A8A4F2"],
          inverseColors: false,
          opacityFrom: 0.9,
          opacityTo: 0.7,
        },
      },
      responsive: [
        {
          breakpoint: 768,
          options: {
            chart: {
              height: 250,
            },
            plotOptions: {
              bar: {
                horizontal: false,
              },
            },
          },
        },
      ],
    }
  }

  /**
   * Rafraîchit toutes les données
   */
  refreshData(): void {
    this.isRefreshing = true
    console.log("🔄 Actualisation des données...")

    this.loadDashboardData()

    // Simuler un délai pour l'UX
    setTimeout(() => {
      this.isRefreshing = false
      this.showSuccess("Données actualisées avec succès")
    }, 1000)
  }

  /**
   * Formate une valeur selon son type
   */
  formatValue(value: number | string, format?: string): string {
    if (typeof value === "string") return value
    if (value === null || value === undefined) return "0"

    switch (format) {
      case "currency":
        return new Intl.NumberFormat("fr-FR", {
          style: "currency",
          currency: "EUR",
        }).format(value)
      case "percentage":
        return `${value.toFixed(1)}%`
      case "number":
      default:
        return new Intl.NumberFormat("fr-FR").format(value)
    }
  }

  /**
   * Retourne la classe CSS pour la variation
   */
  getVariationClass(variation?: number): string {
    if (!variation || variation === 0) return ""
    return variation > 0 ? "text-success" : "text-danger"
  }

  /**
   * Retourne l'icône pour la variation
   */
  getVariationIcon(variation?: number): string {
    if (!variation || variation === 0) return "remove"
    return variation > 0 ? "trending_up" : "trending_down"
  }

  /**
   * Calcule le pourcentage de progression pour les cartes avec barre de progression
   */
  getProgressPercentage(card: KpiCard): number {
    if (card.format === "percentage") {
      return Math.min(card.value as number, 100)
    }
    return Math.min(((card.value as number) / 100) * 100, 100)
  }

  /**
   * Calcule la commission moyenne par commande
   */
  private calculateAverageCommission(): number {
    if (!this.stats) return 0

    const totalCommissions = this.stats.totalCommissionsGenerees || 0
    const totalCommandes = this.stats.totalCommandesConfirmees || 0

    return totalCommandes > 0 ? totalCommissions / totalCommandes : 0
  }

  /**
   * Calcule le montant total des factures en attente
   */
  private calculatePendingInvoicesAmount(): number {
    if (!this.stats) return 0

    const totalFactures = this.stats.montantTotalFactures || 0
    const facturesPayees = this.stats.facturesPayees || 0
    const facturesTotal = this.stats.facturesDisponibles || 0

    if (facturesTotal === 0) return 0

    // Estimation du montant en attente basé sur le ratio
    const ratioPayees = facturesPayees / facturesTotal
    return totalFactures * (1 - ratioPayees)
  }

  /**
   * Retourne des statistiques par défaut en cas d'erreur
   */
  private getDefaultStats(): MarketerDashboardStats {
    return {
      totalProduitsActifs: 0,
      totalProduitsWowPrice: 0,
      variationProduitsVsPrecedent: 0,
      commandesEnAttente: 0,
      variationCommandesEnAttenteVsPrecedent: 0,
      commandesConfirmeesLivrees: 0,
      commandesConfirmeesEnCours: 0,
      totalCommandesConfirmees: 0,
      variationCommandesConfirmeesVsPrecedent: 0,
      tauxConversion: 0,
      variationTauxConversionVsPrecedent: 0,
      chiffreAffairesMois: 0,
      commissionsEnAttente: 0,
      dernierPaiement: 0,
      totalCommissionsGenerees: 0,
      facturesDisponibles: 0,
      montantTotalFactures: 0,
      facturesPayees: 0,
      facturesEnAttenteFacture: 0,
      periodeActuelle: "Décembre 2025",
      periodePrecedente: "Novembre 2025",
    }
  }

  /**
   * Retourne des données de graphiques par défaut
   */
  private getDefaultCharts(): MarketerDashboardCharts {
    return {
      evolutionCommandes: [],
      repartitionProduits: [],
    }
  }

  /**
   * Affiche un message de succès
   */
  private showSuccess(message: string): void {
    this.snackBar.open(message, "Fermer", {
      duration: 3000,
      panelClass: ["success-snackbar"],
    })
  }

  /**
   * Affiche un message d'erreur
   */
  private showError(message: string): void {
    this.snackBar.open(message, "Fermer", {
      duration: 5000,
      panelClass: ["error-snackbar"],
    })
  }

  /**
   * TrackBy functions pour optimiser les performances des *ngFor
   */
  trackByKpiCard(index: number, card: KpiCard): string {
    return card.title
  }

  trackByOrder(index: number, order: RecentOrder): number {
    return order.id
  }

  trackByProduct(index: number, product: TopProduct): number {
    return product.productId
  }
}
