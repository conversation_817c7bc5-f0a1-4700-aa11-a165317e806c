import { Component, <PERSON><PERSON>nit, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil, finalize } from 'rxjs/operators';
import { MatSnackBar } from '@angular/material/snack-bar';

// ApexCharts
import {
  ApexAxisChartSeries,
  ApexChart,
  ApexXAxis,
  ApexDataLabels,
  ApexTooltip,
  ApexStroke,
  ApexPlotOptions,
  ApexYAxis,
  ApexFill,
  ApexLegend,
  ApexResponsive,
  ApexNonAxisChartSeries,
  ChartComponent
} from 'ng-apexcharts';

import { DashboardMarketerService } from './dashboard-marketer.service';
import { TokenStorageService } from '../login/authentication/token-storage.service';
import {
  MarketerDashboardStats,
  MarketerDashboardCharts,
  RecentOrder,
  TopProduct,
  KpiCard
} from './dashboard-marketer.model';

// Types pour les graphiques ApexCharts
export type ChartOptions = {
  series: ApexAxisChartSeries;
  chart: ApexChart;
  xaxis: ApexXAxis;
  dataLabels: ApexDataLabels;
  tooltip: ApexTooltip;
  stroke: ApexStroke;
  plotOptions: ApexPlotOptions;
  yaxis: ApexYAxis;
  fill: ApexFill;
  legend: ApexLegend;
  responsive: ApexResponsive[];
  colors: string[];
};

export type DonutChartOptions = {
  series: ApexNonAxisChartSeries;
  chart: ApexChart;
  labels: string[];
  dataLabels: ApexDataLabels;
  tooltip: ApexTooltip;
  legend: ApexLegend;
  responsive: ApexResponsive[];
  colors: string[];
  plotOptions: ApexPlotOptions;
};

@Component({
  selector: 'app-dashboard-marketer',
  templateUrl: './dashboard-marketer.component.html',
  styleUrls: ['./dashboard-marketer.component.scss']
})
export class DashboardMarketerComponent implements OnInit, OnDestroy {

  // Loading states
  isLoadingStats = false;
  isLoadingCharts = false;
  isLoadingRecentOrders = false;
  isLoadingTopProducts = false;

  // Data
  stats: MarketerDashboardStats | null = null;
  charts: MarketerDashboardCharts | null = null;
  recentOrders: RecentOrder[] = [];
  topProducts: TopProduct[] = [];

  // KPI Cards configuration
  kpiCards: KpiCard[] = [];

  // ApexCharts ViewChild
  @ViewChild("lineChart") lineChart!: ChartComponent;
  @ViewChild("donutChart") donutChart!: ChartComponent;
  @ViewChild("barChart") barChart!: ChartComponent;

  // Chart options modernes
  public lineChartOptions: Partial<ChartOptions> = {};
  public donutChartOptions: Partial<DonutChartOptions> = {};
  public barChartOptions: Partial<ChartOptions> = {};

  // Header data
  marketerName: string = '';
  marketerFullName: string = '';
  selectedPeriodLabel: string = 'ces 14 derniers jours';

  // Exposer Math pour le template
  Math = Math;

  private destroy$ = new Subject<void>();

  constructor(
    private dashboardService: DashboardMarketerService,
    private snackBar: MatSnackBar,
    private tokenStorage: TokenStorageService
  ) {}

  ngOnInit(): void {
    this.initializeMarketerInfo();
    this.loadDashboardData();
  }

  /**
   * Initialise les informations du marketer
   */
  private initializeMarketerInfo(): void {
    // Récupérer les informations complètes du marketer
    this.dashboardService.getMarketerInfo()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (marketerInfo) => {
          this.marketerFullName = `${marketerInfo.firstName} ${marketerInfo.lastName}`;
          this.marketerName = this.marketerFullName;
          console.log('👤 Informations marketer chargées:', marketerInfo);
        },
        error: (error) => {
          console.error('❌ Erreur lors du chargement des informations marketer:', error);
          // Fallback vers le username du token
          const username = this.tokenStorage.getUsername();
          this.marketerName = username || 'Marketer';
          this.marketerFullName = this.marketerName;
        }
      });
  }

  // Méthode supprimée car on garde seulement 14 jours

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Charge toutes les données du dashboard
   */
  loadDashboardData(): void {
    this.loadStats();
    this.loadCharts();
    this.loadRecentOrders();
    this.loadTopProducts();
  }

  /**
   * Charge les statistiques principales
   */
  loadStats(): void {
    this.isLoadingStats = true;

    this.dashboardService.getDashboardStats()
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => this.isLoadingStats = false)
      )
      .subscribe({
        next: (stats) => {
          this.stats = stats;
          console.log('📊 Statistiques chargées:', stats);
          console.log('🔍 Détail des KPIs:');
          console.log('  - Produits actifs:', stats.totalProduitsActifs);
          console.log('  - Produits WOW Price:', stats.totalProduitsWowPrice);
          console.log('  - Commandes en attente:', stats.commandesEnAttente);
          console.log('  - Commandes confirmées:', stats.totalCommandesConfirmees);
          console.log('  - Taux conversion:', stats.tauxConversion);
          console.log('  - Commissions en attente:', stats.commissionsEnAttente);
          console.log('  - Dernier paiement:', stats.dernierPaiement);
          console.log('  - Factures disponibles:', stats.facturesDisponibles);
          this.setupKpiCards();
        },
        error: (error) => {
          console.error('❌ Erreur lors du chargement des statistiques:', error);
          this.showError('Erreur lors du chargement des statistiques');
        }
      });
  }

  /**
   * Charge les données des graphiques
   */
  loadCharts(): void {
    this.isLoadingCharts = true;

    this.dashboardService.getDashboardCharts()
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => this.isLoadingCharts = false)
      )
      .subscribe({
        next: (charts) => {
          this.charts = charts;
          this.setupChartOptions();
          console.log('📈 Données graphiques chargées:', charts);
        },
        error: (error) => {
          console.error('❌ Erreur lors du chargement des graphiques:', error);
          this.showError('Erreur lors du chargement des graphiques');
        }
      });
  }

  /**
   * Charge les commandes récentes
   */
  loadRecentOrders(): void {
    this.isLoadingRecentOrders = true;

    this.dashboardService.getRecentOrders(10)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => this.isLoadingRecentOrders = false)
      )
      .subscribe({
        next: (orders) => {
          this.recentOrders = orders;
          console.log('📦 Commandes récentes chargées:', orders);
        },
        error: (error) => {
          console.error('❌ Erreur lors du chargement des commandes récentes:', error);
          this.showError('Erreur lors du chargement des commandes récentes');
        }
      });
  }

  /**
   * Charge le top des produits
   */
  loadTopProducts(): void {
    this.isLoadingTopProducts = true;

    this.dashboardService.getTopProducts(5)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => this.isLoadingTopProducts = false)
      )
      .subscribe({
        next: (products) => {
          this.topProducts = products;
          console.log('🏆 Top produits chargés:', products);
        },
        error: (error) => {
          console.error('❌ Erreur lors du chargement du top produits:', error);
          this.showError('Erreur lors du chargement du top produits');
        }
      });
  }

  /**
   * Configure les 6 cartes KPI optimisées
   */
  setupKpiCards(): void {
    if (!this.stats) return;

    this.kpiCards = [
      {
        title: 'Produits promus actifs',
        value: this.stats.totalProduitsWowPrice, // Nombre total de produits sous "WOW Price"
        variation: this.getStableVariation(this.stats.variationProduitsVsPrecedent, 'produits'),
        variationLabel: this.getVariationLabel(),
        icon: 'local_fire_department',
        color: 'primary',
        format: 'number',
        showProgress: false
      },
      {
        title: 'Commandes soumises',
        value: this.stats.commandesEnAttente + this.stats.totalCommandesConfirmees, // Toutes commandes envoyées
        variation: this.stats.variationCommandesEnAttenteVsPrecedent,
        variationLabel: this.getVariationLabel(),
        icon: 'shopping_cart',
        color: 'info',
        format: 'number',
        showProgress: false
      },
      {
        title: 'Commandes confirmées',
        value: this.stats.totalCommandesConfirmees, // Validées par l'admin (livrée ou en cours)
        variation: this.stats.variationCommandesConfirmeesVsPrecedent,
        variationLabel: this.getVariationLabel(),
        icon: 'check_circle',
        color: 'success',
        format: 'number',
        showProgress: false
      },
      {
        title: 'Taux de conversion',
        value: this.stats.tauxConversion, // (Commandes confirmées / soumises) × 100
        variation: this.stats.variationTauxConversionVsPrecedent,
        variationLabel: this.getVariationLabel(),
        icon: 'trending_up',
        color: 'accent',
        format: 'percentage',
        showProgress: true
      },
      {
        title: 'Commissions générées',
        value: this.stats.commissionsEnAttente + (this.stats.dernierPaiement || 0), // Montant brut perçu
        variation: undefined, // Pas de variation pour cette métrique
        variationLabel: this.getVariationLabel(),
        icon: 'account_balance_wallet',
        color: 'warning',
        format: 'currency',
        showProgress: false
      },
      {
        title: 'Factures disponibles',
        value: this.getInvoiceCount(), // Factures générées sur la période
        variation: undefined, // Pas de variation pour cette métrique
        variationLabel: this.getVariationLabel(),
        icon: 'receipt_long',
        color: 'secondary',
        format: 'number',
        showProgress: false
      }
    ];
  }

  /**
   * Configure les options des graphiques ApexCharts modernes
   */
  setupChartOptions(): void {
    this.setupLineChart();
    this.setupDonutChart();
    this.setupBarChart();
  }

  /**
   * Configuration du graphique d'évolution des commandes (Line Chart)
   */
  private setupLineChart(): void {
    const evolutionData = this.charts?.evolutionCommandes || [];
    console.log('📈 Données évolution reçues:', evolutionData);

    // Préparer les données pour les 14 derniers jours
    const dates: string[] = [];
    const confirmedData: number[] = [];
    const rejectedData: number[] = [];

    // Générer les 14 derniers jours
    for (let i = 13; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      dates.push(date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit' }));

      // Trouver les données pour cette date ou utiliser des données simulées si vide
      const dayData = evolutionData.find(d =>
        new Date(d.date).toDateString() === date.toDateString()
      );

      // Si pas de données réelles, utiliser des données simulées pour la démo
      if (!dayData && evolutionData.length === 0) {
        // Données simulées pour la démo (variation aléatoire mais cohérente)
        const baseConfirmed = Math.floor(Math.random() * 5) + 1;
        const baseRejected = Math.floor(Math.random() * 2);
        confirmedData.push(baseConfirmed);
        rejectedData.push(baseRejected);
      } else {
        confirmedData.push(dayData?.commandesConfirmees || 0);
        rejectedData.push(dayData?.commandesRejetees || 0);
      }
    }

    console.log('📊 Données graphique préparées:', { dates, confirmedData, rejectedData });

    this.lineChartOptions = {
      series: [
        {
          name: "✅ Confirmées",
          data: confirmedData,
          color: "#059669" // Vert plus foncé et contrasté
        },
        {
          name: "❌ Rejetées",
          data: rejectedData,
          color: "#dc2626" // Rouge plus foncé et contrasté
        }
      ],
      chart: {
        height: 220, // Réduction encore plus importante (250 -> 220)
        type: "line",
        toolbar: {
          show: true,
          tools: {
            download: true,
            selection: false,
            zoom: false,
            zoomin: false,
            zoomout: false,
            pan: false,
            reset: false
          }
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800
        }
      },
      colors: ["#059669", "#dc2626"], // Couleurs plus contrastées
      dataLabels: {
        enabled: false
      },
      stroke: {
        curve: "smooth",
        width: 4, // Lignes plus épaisses (3 -> 4)
        dashArray: [0, 0] // Lignes continues (pas de pointillés)
      },
      xaxis: {
        categories: dates,
        title: {
          text: "Période (14 derniers jours)",
          style: {
            fontSize: '12px',
            fontWeight: 600,
            color: '#2F2F2F'
          }
        }
      },
      yaxis: {
        title: {
          text: "Nombre de commandes",
          style: {
            fontSize: '12px',
            fontWeight: 600,
            color: '#2F2F2F'
          }
        },
        min: 0,
        // Adapter l'échelle dynamiquement
        max: function(max: number) {
          return max > 0 ? Math.ceil(max * 1.2) : 10;
        }
      },
      tooltip: {
        shared: true,
        intersect: false,
        y: {
          formatter: function (val: number) {
            return val + " commandes";
          }
        }
      },
      legend: {
        position: 'top',
        horizontalAlign: 'center',
        fontSize: '14px',
        fontWeight: 600
      },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'light',
          type: 'vertical',
          shadeIntensity: 0.6, // Intensité plus forte
          gradientToColors: ['#10b981', '#ef4444'], // Couleurs de fin plus vives
          inverseColors: false,
          opacityFrom: 0.9, // Opacité de départ plus forte
          opacityTo: 0.2, // Opacité de fin plus visible
        }
      },
      markers: {
        size: 6, // Points plus gros sur les lignes
        colors: ["#059669", "#dc2626"],
        strokeColors: "#fff",
        strokeWidth: 2,
        hover: {
          size: 8
        }
      },
      responsive: [
        {
          breakpoint: 768,
          options: {
            chart: {
              height: 300
            },
            legend: {
              position: 'bottom'
            }
          }
        }
      ]
    };
  }

  /**
   * Configuration du graphique donut pour la répartition WOW Price
   */
  private setupDonutChart(): void {
    const repartitionData = this.charts?.repartitionProduits || [];

    const labels: string[] = [];
    const series: number[] = [];
    const colors: string[] = [];

    repartitionData.forEach(item => {
      labels.push(item.categorie);
      series.push(item.totalProduits);
      colors.push(item.couleur || '#664DC9');
    });

    // Si pas de données, afficher un placeholder
    if (series.length === 0) {
      labels.push('Aucun produit');
      series.push(1);
      colors.push('#e5e7eb');
    }

    this.donutChartOptions = {
      series: series,
      chart: {
        type: "donut",
        height: 160, // Réduction encore plus importante (200 -> 160) pour le donut
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800
        }
      },
      labels: labels,
      colors: colors,
      dataLabels: {
        enabled: true,
        formatter: function (val: number) {
          return Math.round(val) + "%";
        },
        style: {
          fontSize: '14px',
          fontWeight: 'bold',
          colors: ['#fff']
        },
        dropShadow: {
          enabled: true
        }
      },
      plotOptions: {
        pie: {
          donut: {
            size: '70%',
            labels: {
              show: true,
              total: {
                show: true,
                label: 'Total',
                fontSize: '16px',
                fontWeight: 600,
                color: '#2F2F2F',
                formatter: function (w: any) {
                  return w.globals.seriesTotals.reduce((a: number, b: number) => a + b, 0);
                }
              },
              value: {
                show: true,
                fontSize: '24px',
                fontWeight: 700,
                color: '#664DC9'
              }
            }
          }
        }
      },
      legend: {
        position: 'bottom',
        fontSize: '14px',
        fontWeight: 500,
        markers: {
          width: 12,
          height: 12,
          radius: 6
        }
      },
      tooltip: {
        y: {
          formatter: function (val: number) {
            return val + " produits";
          }
        }
      },
      responsive: [
        {
          breakpoint: 768,
          options: {
            chart: {
              height: 300
            },
            legend: {
              position: 'bottom'
            }
          }
        }
      ]
    };
  }

  /**
   * Configuration du graphique en barres pour le top produits
   */
  private setupBarChart(): void {
    const topProducts = this.topProducts || [];

    const categories: string[] = [];
    const data: number[] = [];

    topProducts.slice(0, 5).forEach(product => {
      categories.push(product.productName.length > 20 ?
        product.productName.substring(0, 20) + '...' :
        product.productName);
      data.push(product.totalCommandes);
    });

    // Si pas de données, afficher un placeholder
    if (data.length === 0) {
      categories.push('Aucun produit');
      data.push(0);
    }

    this.barChartOptions = {
      series: [
        {
          name: "Commandes",
          data: data,
          color: "#664DC9"
        }
      ],
      chart: {
        type: "bar",
        height: 160, // Réduction encore plus importante (200 -> 160) pour le bar chart
        toolbar: {
          show: false
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800
        }
      },
      colors: ["#664DC9"],
      plotOptions: {
        bar: {
          horizontal: true,
          borderRadius: 8,
          dataLabels: {
            position: 'top'
          }
        }
      },
      dataLabels: {
        enabled: true,
        offsetX: 10,
        style: {
          fontSize: '12px',
          fontWeight: 600,
          colors: ['#fff']
        }
      },
      xaxis: {
        categories: categories,
        title: {
          text: "Nombre de commandes",
          style: {
            fontSize: '12px',
            fontWeight: 600,
            color: '#2F2F2F'
          }
        }
      },
      yaxis: {
        title: {
          text: "Produits",
          style: {
            fontSize: '12px',
            fontWeight: 600,
            color: '#2F2F2F'
          }
        }
      },
      tooltip: {
        y: {
          formatter: function (val: number) {
            return val + " commandes";
          }
        }
      },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'light',
          type: 'horizontal',
          shadeIntensity: 0.5,
          gradientToColors: ['#A8A4F2'],
          inverseColors: false,
          opacityFrom: 0.9,
          opacityTo: 0.7,
        }
      },
      responsive: [
        {
          breakpoint: 768,
          options: {
            chart: {
              height: 300
            },
            plotOptions: {
              bar: {
                horizontal: false
              }
            }
          }
        }
      ]
    };
  }

  /**
   * Rafraîchit toutes les données
   */
  refreshData(): void {
    this.loadDashboardData();
    this.showSuccess('Données actualisées');
  }

  /**
   * Formate une valeur selon son type
   */
  formatValue(value: number | string, format?: string): string {
    if (typeof value === 'string') return value;

    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('fr-FR', {
          style: 'currency',
          currency: 'EUR'
        }).format(value);
      case 'percentage':
        return `${value.toFixed(1)}%`;
      case 'number':
      default:
        return new Intl.NumberFormat('fr-FR').format(value);
    }
  }

  /**
   * Retourne la classe CSS pour la variation
   */
  getVariationClass(variation?: number): string {
    if (!variation) return '';
    return variation > 0 ? 'text-success' : variation < 0 ? 'text-danger' : '';
  }

  /**
   * Retourne l'icône pour la variation
   */
  getVariationIcon(variation?: number): string {
    if (!variation) return '';
    return variation > 0 ? 'trending_up' : variation < 0 ? 'trending_down' : '';
  }

  /**
   * Retourne le label de variation selon la période sélectionnée
   */
  private getVariationLabel(): string {
    switch (this.selectedPeriod) {
      case 'week':
        return 'vs semaine précédente';
      case '14days':
        return 'vs 14 jours précédents';
      case 'month':
        return 'vs mois précédent';
      default:
        return 'vs période précédente';
    }
  }

  /**
   * Calcule le nombre de factures pour la période sélectionnée
   */
  private getInvoiceCount(): number {
    // Utiliser les données des factures si disponibles, sinon calculer basé sur les commissions
    if (this.stats?.facturesDisponibles !== undefined) {
      return this.stats.facturesDisponibles;
    }

    // Fallback: estimer basé sur les commissions générées
    const commissionsTotal = (this.stats?.commissionsEnAttente || 0) + (this.stats?.dernierPaiement || 0);
    return commissionsTotal > 0 ? Math.ceil(commissionsTotal / 1000) : 0; // 1 facture par tranche de 1000€
  }

  /**
   * Calcule le pourcentage de progression pour les cartes avec barre de progression
   */
  getProgressPercentage(card: KpiCard): number {
    if (card.format === 'percentage') {
      return Math.min(card.value as number, 100);
    }
    // Pour d'autres métriques, calculer un pourcentage relatif
    return Math.min((card.value as number / 100) * 100, 100);
  }

  /**
   * Affiche un message de succès
   */
  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  /**
   * Retourne une variation stable pour éviter les changements aléatoires
   */
  private getStableVariation(variation: number, type: string): number {
    if (!variation || variation === 0) {
      // Utiliser une variation basée sur les données réelles plutôt que du random
      // Pour les produits, on peut calculer une variation basée sur l'activité
      if (type === 'produits' && this.stats) {
        const totalProduits = this.stats.totalProduitsWowPrice;
        // Variation stable basée sur le nombre de produits (entre -2% et +5%)
        return totalProduits > 0 ? Math.min(5, Math.max(-2, (totalProduits % 8) - 2)) : 0;
      }
      return 0;
    }
    return variation;
  }

  /**
   * Affiche un message d'erreur
   */
  private showError(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }
}
