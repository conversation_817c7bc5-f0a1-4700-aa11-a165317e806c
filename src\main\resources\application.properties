logging.level.org.springframework = info
#spring.datasource.url = *****************************************************************************
spring.datasource.url = *************************************************************************
spring.datasource.username = root
spring.datasource.password = @r00T@sQl

#spring.datasource.password = StrongPassword123!
# true
spring.jpa.show-sql = false
spring.jpa.properties.hibernate.dialect = org.hibernate.dialect.MySQL5InnoDBDialect
spring.jpa.hibernate.ddl-auto=update

spring.jackson.serialization.FAIL_ON_EMPTY_BEANS=false


server.tomcat.uri-encoding=UTF-8
#server.port=8080
server.port=8091

springdoc.swagger-ui.title=Eagle Doc Api
#springdoc.swagger-ui.path=/api-docs

# App Properties
grokonez.app.jwtSecret=jwtGrokonezSecretKey
grokonez.app.jwtExpiration=144000

# application.properties
#spring.cache.ehcache.config=classpath:ehcache.xml


spring.servlet.multipart.enabled=true

# ========== NOTIFICATIONS EMAIL ==========
# Activer les notifications par email
notifications.email.enabled=true

# Configuration des taches asynchrones pour l'envoi d'emails
spring.task.execution.pool.core-size=2
spring.task.execution.pool.max-size=5
spring.task.execution.pool.queue-capacity=100

#MAIL CONFIGURATION FOR AFFILINK
mail.smtp.host=smtp.gmail.com
mail.smtp.port=587
mail.smtp.user=<EMAIL>
mail.smtp.pwd=bnwu tzvv kqml xekm
mail.click=<EMAIL>

# Spring Boot Mail Configuration
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=bnwu tzvv kqml xekm
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
spring.mail.properties.mail.smtp.ssl.trust=smtp.gmail.com
spring.mail.properties.mail.debug=true

spring.resources.static-locations=classpath:/static/,file:uploads/

# File upload configuration
file.upload-dir=./uploads
file.max-image-size=5242880
file.max-video-size=52428800

# Spring servlet multipart configuration
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB

# PDF Generation configuration
app.pdf.storage.path=./pdfs/factures

spring.cache.type=simple
spring.cache.cache-names=dashboard-stats,dashboard-charts,marketer-info
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html